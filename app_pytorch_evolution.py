#!/usr/bin/env python3
"""
NeuroScan avec PyTorch et système d'évolution tumorale complet
"""

from flask import Flask, render_template, request, jsonify, Response
import sqlite3
import json
import os
import base64
import time
from datetime import datetime, timedelta
from werkzeug.utils import secure_filename

# Imports PyTorch
import torch
import torch.nn as nn
import torch.nn.functional as F
from torchvision import transforms
from PIL import Image
import numpy as np

app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max

# Configuration de la base de données
DATABASE_PATH = 'neuroscan_analytics.db'

# Classes de tumeurs
TUMOR_CLASSES = ['Normal', 'Gliome', 'Méningiome', 'Tumeur pituitaire']

# Configuration PyTorch
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model = None

# Transformations pour les images
transform = transforms.Compose([
    transforms.Resize((224, 224)),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
])

class BrainTumorCNN(nn.Module):
    def __init__(self, num_classes=4):
        super(BrainTumorCNN, self).__init__()
        
        # Couches de convolution
        self.conv1 = nn.Conv2d(3, 32, kernel_size=3, padding=1)
        self.conv2 = nn.Conv2d(32, 64, kernel_size=3, padding=1)
        self.conv3 = nn.Conv2d(64, 128, kernel_size=3, padding=1)
        self.conv4 = nn.Conv2d(128, 256, kernel_size=3, padding=1)
        self.conv5 = nn.Conv2d(256, 256, kernel_size=3, padding=1)
        
        # Couches de pooling et autres
        self.pool = nn.MaxPool2d(2, 2)
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.5)
        self.flatten = nn.Flatten()
        
        # Couches fully connected
        self.fc1 = nn.Linear(12544, 512)
        self.fc2 = nn.Linear(512, 4)
        
    def forward(self, x):
        # Appliquer les couches de convolution avec pooling
        x = self.pool(self.relu(self.conv1(x)))
        x = self.pool(self.relu(self.conv2(x)))
        x = self.pool(self.relu(self.conv3(x)))
        x = self.pool(self.relu(self.conv4(x)))
        x = self.pool(self.relu(self.conv5(x)))
        
        # Aplatir et appliquer les couches FC
        x = self.flatten(x)
        x = self.dropout(self.relu(self.fc1(x)))
        x = self.fc2(x)
        
        return x

def load_model():
    """Charger le modèle PyTorch"""
    global model
    try:
        model = BrainTumorCNN(num_classes=4)
        model.load_state_dict(torch.load('best_brain_tumor_model.pth', map_location=device))
        model.to(device)
        model.eval()
        print(f"✅ Modèle PyTorch chargé avec succès sur {device}")
        return True
    except Exception as e:
        print(f"❌ Erreur lors du chargement du modèle: {e}")
        return False

def init_database():
    """Initialiser la base de données avec les tables nécessaires"""
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()

    # Table des patients
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS patients (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            patient_id TEXT UNIQUE NOT NULL,
            nom TEXT,
            prenom TEXT,
            date_naissance DATE,
            sexe TEXT CHECK(sexe IN ('M', 'F', 'Autre')),
            medecin_referent TEXT,
            notes_medicales TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Table des examens IRM
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS examens_irm (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            patient_id TEXT NOT NULL,
            exam_date DATETIME NOT NULL,
            filename TEXT,
            image_metadata TEXT,
            predicted_class INTEGER,
            predicted_label TEXT,
            confidence REAL,
            probabilities TEXT,
            description TEXT,
            recommendations TEXT,
            processing_time REAL,
            user_session TEXT,
            ip_address TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (patient_id) REFERENCES patients(patient_id)
        )
    ''')

    # Table d'analyse comparative d'évolution
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS evolution_analysis (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            patient_id TEXT NOT NULL,
            exam_id_previous INTEGER,
            exam_id_current INTEGER,
            time_interval_days INTEGER,
            evolution_type TEXT CHECK(evolution_type IN ('stable', 'progression', 'regression', 'changement_type')),
            confidence_change REAL,
            probability_changes TEXT,
            volume_change_estimate TEXT,
            alert_level TEXT CHECK(alert_level IN ('none', 'low', 'medium', 'high', 'critical')),
            medical_interpretation TEXT,
            recommendations TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (patient_id) REFERENCES patients(patient_id),
            FOREIGN KEY (exam_id_previous) REFERENCES examens_irm(id),
            FOREIGN KEY (exam_id_current) REFERENCES examens_irm(id)
        )
    ''')

    # Index pour optimiser les requêtes temporelles
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_patient_exam_date ON examens_irm(patient_id, exam_date)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_evolution_patient ON evolution_analysis(patient_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_evolution_alert ON evolution_analysis(alert_level)')

    conn.commit()
    conn.close()

def create_or_get_patient(patient_id, nom=None, prenom=None, date_naissance=None, sexe=None, medecin_referent=None):
    """Créer un nouveau patient ou récupérer un patient existant"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()
        
        # Vérifier si le patient existe déjà
        cursor.execute('SELECT * FROM patients WHERE patient_id = ?', (patient_id,))
        existing_patient = cursor.fetchone()
        
        if existing_patient:
            # Mettre à jour la date de dernière modification
            cursor.execute('''
                UPDATE patients SET updated_at = CURRENT_TIMESTAMP WHERE patient_id = ?
            ''', (patient_id,))
            conn.commit()
            conn.close()
            return existing_patient[0]  # Retourner l'ID
        else:
            # Créer un nouveau patient
            cursor.execute('''
                INSERT INTO patients (patient_id, nom, prenom, date_naissance, sexe, medecin_referent)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (patient_id, nom, prenom, date_naissance, sexe, medecin_referent))
            
            patient_db_id = cursor.lastrowid
            conn.commit()
            conn.close()
            return patient_db_id
            
    except Exception as e:
        print(f"Erreur lors de la création/récupération du patient: {e}")
        return None

def preprocess_image(image_path):
    """Préprocesser l'image pour le modèle PyTorch"""
    try:
        # Charger l'image
        image = Image.open(image_path)
        
        # Convertir en RGB si nécessaire
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # Appliquer les transformations
        image_tensor = transform(image).unsqueeze(0)  # Ajouter dimension batch
        
        return image_tensor
    except Exception as e:
        print(f"Erreur lors du préprocessing: {e}")
        return None

def predict_tumor(image_path):
    """Prédire le type de tumeur avec PyTorch"""
    if model is None:
        print("❌ Modèle non chargé")
        return None
    
    try:
        # Préprocesser l'image
        image_tensor = preprocess_image(image_path)
        if image_tensor is None:
            return None
        
        # Faire la prédiction
        with torch.no_grad():
            image_tensor = image_tensor.to(device)
            outputs = model(image_tensor)
            probabilities = F.softmax(outputs, dim=1)
            
            # Obtenir les probabilités pour chaque classe
            probs = probabilities.cpu().numpy()[0]
            predicted_class = np.argmax(probs)
            
            results = {
                'predicted_class': int(predicted_class),
                'predicted_label': TUMOR_CLASSES[predicted_class],
                'confidence': float(probs[predicted_class]),
                'probabilities': {
                    'Normal': float(probs[0]),
                    'Gliome': float(probs[1]),
                    'Méningiome': float(probs[2]),
                    'Tumeur pituitaire': float(probs[3])
                }
            }

            # Ajouter description et recommandations
            results['description'] = generate_description(results)
            results['recommendations'] = generate_recommendations(results)

            return results
            
    except Exception as e:
        print(f"Erreur lors de la prédiction: {e}")
        return None

def generate_description(results):
    """Générer une description médicale"""
    label = results['predicted_label']
    confidence = results['confidence']
    
    descriptions = {
        'Normal': f"Structures cérébrales normales détectées avec {confidence:.1%} de confiance. Aucune anomalie significative observée.",
        'Gliome': f"Gliome détecté avec {confidence:.1%} de confiance. Tumeur gliale nécessitant une évaluation neurologique approfondie.",
        'Méningiome': f"Méningiome identifié avec {confidence:.1%} de confiance. Tumeur des méninges généralement bénigne.",
        'Tumeur pituitaire': f"Tumeur pituitaire détectée avec {confidence:.1%} de confiance. Anomalie hypophysaire nécessitant un bilan endocrinologique."
    }
    
    return descriptions.get(label, f"Analyse: {label} ({confidence:.1%})")

def generate_recommendations(results):
    """Générer des recommandations médicales"""
    label = results['predicted_label']
    confidence = results['confidence']
    
    recommendations = {
        'Normal': [
            "Suivi de routine recommandé",
            "Maintenir les contrôles périodiques selon protocole",
            "Aucune intervention immédiate nécessaire"
        ],
        'Gliome': [
            "Consultation neurochirurgicale urgente",
            "IRM avec contraste recommandée",
            "Biopsie stéréotaxique à considérer",
            "Évaluation multidisciplinaire en neuro-oncologie"
        ],
        'Méningiome': [
            "Consultation neurochirurgicale",
            "Surveillance radiologique régulière",
            "Évaluation des symptômes neurologiques",
            "Considérer traitement selon taille et localisation"
        ],
        'Tumeur pituitaire': [
            "Consultation endocrinologique",
            "Bilan hormonal complet",
            "IRM hypophysaire avec coupes fines",
            "Évaluation ophtalmologique (champ visuel)"
        ]
    }
    
    base_recs = recommendations.get(label, ["Consultation spécialisée recommandée"])
    
    # Ajouter recommandation basée sur la confiance
    if confidence < 0.7:
        base_recs.append("⚠️ Confiance modérée - Confirmer par expertise médicale")
    elif confidence > 0.9:
        base_recs.append("✅ Haute confiance du diagnostic IA")
    
    return base_recs

def save_exam_to_db(patient_id, results, filename, processing_time, exam_date=None, 
                   image_metadata=None, session_id=None, ip_address=None):
    """Sauvegarder un examen IRM dans la nouvelle structure"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        # Utiliser la date actuelle si non spécifiée
        if exam_date is None:
            exam_date = datetime.now()

        # Convertir les données en JSON
        probabilities_json = json.dumps(results['probabilities'])
        recommendations_json = json.dumps(results.get('recommendations', []))
        metadata_json = json.dumps(image_metadata) if image_metadata else None

        cursor.execute('''
            INSERT INTO examens_irm
            (patient_id, exam_date, filename, image_metadata, predicted_class, predicted_label, 
             confidence, probabilities, description, recommendations, processing_time, 
             user_session, ip_address)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            patient_id,
            exam_date,
            filename,
            metadata_json,
            results['predicted_class'],
            results['predicted_label'],
            results['confidence'],
            probabilities_json,
            results.get('description', ''),
            recommendations_json,
            processing_time,
            session_id,
            ip_address
        ))

        exam_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return exam_id
        
    except Exception as e:
        print(f"Erreur lors de la sauvegarde de l'examen: {e}")
        return None

# Initialiser la base de données et charger le modèle au démarrage
init_database()
model_loaded = load_model()

@app.route('/')
def index():
    """Page d'accueil"""
    return render_template('index.html')

@app.route('/evolution-dashboard')
def evolution_dashboard():
    """Page du tableau de bord d'évolution tumorale"""
    return render_template('evolution_dashboard.html')

def analyze_tumor_evolution(patient_id, current_exam_id):
    """Analyser l'évolution tumorale en comparant avec les examens précédents"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        # Récupérer l'examen actuel
        cursor.execute('SELECT * FROM examens_irm WHERE id = ?', (current_exam_id,))
        current_exam = cursor.fetchone()

        if not current_exam:
            return None

        # Récupérer l'examen précédent le plus récent
        cursor.execute('''
            SELECT * FROM examens_irm
            WHERE patient_id = ? AND exam_date < ?
            ORDER BY exam_date DESC
            LIMIT 1
        ''', (patient_id, current_exam[2]))  # exam_date est à l'index 2

        previous_exam = cursor.fetchone()

        if not previous_exam:
            # Premier examen pour ce patient
            conn.close()
            return {
                'is_first_exam': True,
                'message': 'Premier examen pour ce patient - pas de comparaison possible'
            }

        # Calculer l'évolution
        evolution_data = calculate_evolution_metrics(previous_exam, current_exam)

        # Sauvegarder l'analyse d'évolution
        if evolution_data:
            save_evolution_analysis(cursor, patient_id, previous_exam[0], current_exam_id, evolution_data)

        conn.commit()
        conn.close()

        return evolution_data

    except Exception as e:
        print(f"Erreur lors de l'analyse d'évolution: {e}")
        return None

def calculate_evolution_metrics(previous_exam, current_exam):
    """Calculer les métriques d'évolution entre deux examens"""
    try:
        # Parser les probabilités JSON
        prev_probs = json.loads(previous_exam[8])  # probabilities à l'index 8
        curr_probs = json.loads(current_exam[8])

        # Calculer l'intervalle de temps
        prev_date = datetime.fromisoformat(previous_exam[2])
        curr_date = datetime.fromisoformat(current_exam[2])
        time_interval = (curr_date - prev_date).days

        # Analyser les changements de probabilités
        prob_changes = {}
        max_change = 0
        max_change_type = None

        for tumor_type in prev_probs.keys():
            change = curr_probs[tumor_type] - prev_probs[tumor_type]
            prob_changes[tumor_type] = {
                'previous': prev_probs[tumor_type],
                'current': curr_probs[tumor_type],
                'change': change,
                'change_percent': (change / prev_probs[tumor_type] * 100) if prev_probs[tumor_type] > 0 else 0
            }

            if abs(change) > abs(max_change):
                max_change = change
                max_change_type = tumor_type

        # Déterminer le type d'évolution
        evolution_type = determine_evolution_type(previous_exam, current_exam, prob_changes)

        # Calculer le niveau d'alerte
        alert_level = calculate_alert_level(evolution_type, prob_changes, time_interval)

        # Générer l'interprétation médicale
        medical_interpretation = generate_medical_interpretation(
            previous_exam, current_exam, prob_changes, evolution_type, time_interval
        )

        return {
            'is_first_exam': False,
            'time_interval_days': time_interval,
            'evolution_type': evolution_type,
            'confidence_change': current_exam[7] - previous_exam[7],  # confidence à l'index 7
            'probability_changes': prob_changes,
            'alert_level': alert_level,
            'medical_interpretation': medical_interpretation,
            'previous_diagnosis': previous_exam[6],  # predicted_label à l'index 6
            'current_diagnosis': current_exam[6],
            'max_change_type': max_change_type,
            'max_change_value': max_change
        }

    except Exception as e:
        print(f"Erreur lors du calcul des métriques: {e}")
        return None

def determine_evolution_type(previous_exam, current_exam, prob_changes):
    """Déterminer le type d'évolution basé sur les changements"""
    prev_label = previous_exam[6]  # predicted_label
    curr_label = current_exam[6]

    # Changement de diagnostic principal
    if prev_label != curr_label:
        return 'changement_type'

    # Analyser les changements de probabilités pour le diagnostic actuel
    current_prob_change = prob_changes[curr_label]['change']

    if abs(current_prob_change) < 0.05:  # Changement < 5%
        return 'stable'
    elif current_prob_change > 0:
        return 'progression'
    else:
        return 'regression'

def calculate_alert_level(evolution_type, prob_changes, time_interval):
    """Calculer le niveau d'alerte basé sur l'évolution"""

    # Changement de type de tumeur
    if evolution_type == 'changement_type':
        # Vérifier si c'est un changement vers une tumeur maligne
        for tumor_type, change_data in prob_changes.items():
            if tumor_type in ['Gliome'] and change_data['change'] > 0.3:
                return 'critical'
        return 'high'

    # Progression rapide
    if evolution_type == 'progression':
        max_increase = max([change['change'] for change in prob_changes.values() if change['change'] > 0], default=0)

        if time_interval < 90 and max_increase > 0.4:  # >40% en moins de 3 mois
            return 'critical'
        elif time_interval < 180 and max_increase > 0.3:  # >30% en moins de 6 mois
            return 'high'
        elif max_increase > 0.2:  # >20%
            return 'medium'
        else:
            return 'low'

    # Régression (amélioration)
    if evolution_type == 'regression':
        return 'low'

    # Stable
    return 'none'

def generate_medical_interpretation(previous_exam, current_exam, prob_changes, evolution_type, time_interval):
    """Générer une interprétation médicale de l'évolution"""

    prev_label = previous_exam[6]
    curr_label = current_exam[6]
    prev_confidence = previous_exam[7]
    curr_confidence = current_exam[7]

    interpretation = []

    # Analyse du diagnostic principal
    if prev_label != curr_label:
        interpretation.append(f"Changement de diagnostic : {prev_label} → {curr_label}")
        interpretation.append(f"Confiance : {prev_confidence:.1%} → {curr_confidence:.1%}")
    else:
        interpretation.append(f"Diagnostic stable : {curr_label}")
        confidence_change = curr_confidence - prev_confidence
        if abs(confidence_change) > 0.1:
            direction = "augmentation" if confidence_change > 0 else "diminution"
            interpretation.append(f"Confiance du diagnostic : {direction} de {abs(confidence_change):.1%}")

    # Analyse temporelle
    if time_interval < 30:
        interpretation.append(f"Suivi rapproché ({time_interval} jours)")
    elif time_interval < 90:
        interpretation.append(f"Suivi à court terme ({time_interval} jours)")
    elif time_interval < 365:
        interpretation.append(f"Suivi à moyen terme ({time_interval} jours)")
    else:
        interpretation.append(f"Suivi à long terme ({time_interval} jours)")

    # Analyse des changements significatifs
    significant_changes = []
    for tumor_type, change_data in prob_changes.items():
        if abs(change_data['change']) > 0.1:  # Changement > 10%
            direction = "↗" if change_data['change'] > 0 else "↘"
            significant_changes.append(f"{tumor_type}: {direction} {abs(change_data['change']):.1%}")

    if significant_changes:
        interpretation.append("Changements significatifs : " + ", ".join(significant_changes))

    # Recommandations basées sur l'évolution
    if evolution_type == 'changement_type':
        interpretation.append("⚠️ Recommandation : Réévaluation clinique urgente recommandée")
    elif evolution_type == 'progression':
        interpretation.append("📈 Recommandation : Surveillance renforcée et consultation spécialisée")
    elif evolution_type == 'regression':
        interpretation.append("📉 Évolution favorable - Maintenir le suivi habituel")
    else:
        interpretation.append("📊 Évolution stable - Suivi de routine")

    return " | ".join(interpretation)

def save_evolution_analysis(cursor, patient_id, prev_exam_id, curr_exam_id, evolution_data):
    """Sauvegarder l'analyse d'évolution dans la base de données"""
    try:
        cursor.execute('''
            INSERT INTO evolution_analysis
            (patient_id, exam_id_previous, exam_id_current, time_interval_days,
             evolution_type, confidence_change, probability_changes, alert_level,
             medical_interpretation, recommendations)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            patient_id,
            prev_exam_id,
            curr_exam_id,
            evolution_data['time_interval_days'],
            evolution_data['evolution_type'],
            evolution_data['confidence_change'],
            json.dumps(evolution_data['probability_changes']),
            evolution_data['alert_level'],
            evolution_data['medical_interpretation'],
            json.dumps([])  # Recommandations à implémenter
        ))

        return True

    except Exception as e:
        print(f"Erreur lors de la sauvegarde de l'analyse d'évolution: {e}")
        return False

@app.route('/upload-patient', methods=['POST'])
def upload_patient_exam():
    """Upload et analyse d'IRM avec suivi patient"""
    if not model_loaded:
        return jsonify({'error': 'Modèle PyTorch non disponible'}), 500

    start_time = time.time()

    try:
        # Vérifier les données du formulaire
        if 'file' not in request.files:
            return jsonify({'error': 'Aucun fichier fourni'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'Aucun fichier sélectionné'}), 400

        # Récupérer les informations du patient
        patient_id = request.form.get('patient_id')
        if not patient_id:
            return jsonify({'error': 'ID patient requis'}), 400

        patient_nom = request.form.get('patient_nom', '')
        patient_prenom = request.form.get('patient_prenom', '')
        patient_date_naissance = request.form.get('patient_date_naissance')
        patient_sexe = request.form.get('patient_sexe')
        medecin_referent = request.form.get('medecin_referent', '')
        exam_date = request.form.get('exam_date')

        # Créer ou récupérer le patient
        create_or_get_patient(
            patient_id, patient_nom, patient_prenom,
            patient_date_naissance, patient_sexe, medecin_referent
        )

        # Traitement de l'image
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)

        # Créer le dossier uploads s'il n'existe pas
        os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
        file.save(filepath)

        # Analyser avec PyTorch
        results = predict_tumor(filepath)
        if results is None:
            os.remove(filepath)
            return jsonify({'error': 'Erreur lors de l\'analyse de l\'image'}), 500

        processing_time = time.time() - start_time

        # Encoder l'image en base64
        with open(filepath, 'rb') as img_file:
            img_data = base64.b64encode(img_file.read()).decode('utf-8')
            img_url = f"data:image/jpeg;base64,{img_data}"

        # Nettoyer le fichier temporaire
        os.remove(filepath)

        # Sauvegarder l'examen
        exam_date_obj = datetime.fromisoformat(exam_date) if exam_date else None
        image_metadata = {
            'filename': filename,
            'upload_time': datetime.now().isoformat(),
            'file_size': len(img_data),
            'model_version': 'PyTorch CNN v1.0',
            'device_used': str(device)
        }

        session_id = request.headers.get('X-Session-ID', 'pytorch_session')
        ip_address = request.remote_addr

        exam_id = save_exam_to_db(
            patient_id, results, filename, processing_time,
            exam_date_obj, image_metadata, session_id, ip_address
        )

        if exam_id is None:
            return jsonify({'error': 'Erreur lors de la sauvegarde'}), 500

        # Analyser l'évolution
        evolution_analysis = analyze_tumor_evolution(patient_id, exam_id)

        # Préparer la réponse
        response = {
            'success': True,
            'exam_id': exam_id,
            'patient_id': patient_id,
            'image_url': img_url,
            'prediction': results['predicted_label'],
            'confidence': results['confidence'],
            'probabilities': results['probabilities'],
            'is_tumor': results['predicted_class'] != 0,
            'recommendations': results.get('recommendations', []),
            'description': results.get('description', ''),
            'processing_time': round(processing_time, 2),
            'evolution_analysis': evolution_analysis,
            'model_info': {
                'type': 'PyTorch CNN',
                'device': str(device),
                'classes': TUMOR_CLASSES
            }
        }

        return jsonify(response)

    except Exception as e:
        print(f"Erreur lors de l'upload patient: {e}")
        return jsonify({'error': 'Erreur interne du serveur'}), 500

@app.route('/api/evolution/summary')
def get_evolution_summary():
    """API pour obtenir un résumé de tous les patients avec évolution"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        # Récupérer tous les patients avec leurs examens
        cursor.execute('''
            SELECT p.patient_id, p.nom, p.prenom,
                   COUNT(e.id) as total_exams,
                   MIN(e.exam_date) as first_exam,
                   MAX(e.exam_date) as last_exam,
                   COUNT(ea.id) as evolution_analyses
            FROM patients p
            LEFT JOIN examens_irm e ON p.patient_id = e.patient_id
            LEFT JOIN evolution_analysis ea ON p.patient_id = ea.patient_id
            GROUP BY p.patient_id, p.nom, p.prenom
            HAVING total_exams > 0
            ORDER BY last_exam DESC
        ''')

        patients = cursor.fetchall()

        # Récupérer les alertes actives
        cursor.execute('''
            SELECT patient_id, alert_level, COUNT(*) as count
            FROM evolution_analysis
            WHERE alert_level IN ('critical', 'high')
            AND created_at >= datetime('now', '-30 days')
            GROUP BY patient_id, alert_level
        ''')

        alerts = cursor.fetchall()
        conn.close()

        # Organiser les données
        patients_data = []
        for patient in patients:
            patient_alerts = [a for a in alerts if a[0] == patient[0]]
            critical_alerts = sum(a[2] for a in patient_alerts if a[1] == 'critical')
            high_alerts = sum(a[2] for a in patient_alerts if a[1] == 'high')

            patients_data.append({
                'patient_id': patient[0],
                'nom': patient[1],
                'prenom': patient[2],
                'total_exams': patient[3],
                'first_exam': patient[4],
                'last_exam': patient[5],
                'evolution_analyses': patient[6],
                'critical_alerts': critical_alerts,
                'high_alerts': high_alerts,
                'has_alerts': critical_alerts > 0 or high_alerts > 0
            })

        return jsonify({
            'success': True,
            'total_patients': len(patients_data),
            'patients': patients_data,
            'model_info': {
                'pytorch_loaded': model_loaded,
                'device': str(device),
                'classes': TUMOR_CLASSES
            }
        })

    except Exception as e:
        print(f"Erreur lors de la récupération du résumé: {e}")
        return jsonify({'error': 'Erreur interne du serveur'}), 500

@app.route('/patient/<patient_id>/generate-evolution-report')
def generate_patient_evolution_report(patient_id):
    """Générer un rapport d'évolution HTML pour un patient"""
    try:
        from evolution_report_generator import generate_html_evolution_report

        # Générer le rapport HTML
        output_file = generate_html_evolution_report(patient_id)

        if output_file:
            # Lire le contenu du fichier
            with open(output_file, 'r', encoding='utf-8') as f:
                html_content = f.read()

            # Supprimer le fichier temporaire
            os.remove(output_file)

            return Response(html_content, mimetype='text/html')
        else:
            return jsonify({'error': 'Impossible de générer le rapport'}), 500

    except Exception as e:
        print(f"Erreur lors de la génération du rapport: {e}")
        return jsonify({'error': 'Erreur interne du serveur'}), 500

if __name__ == '__main__':
    print("🧠 Démarrage de NeuroScan - Système d'Évolution Tumorale avec PyTorch")
    print(f"🔥 Modèle PyTorch: {'✅ Chargé' if model_loaded else '❌ Erreur'}")
    print(f"💻 Device: {device}")
    print(f"🎯 Classes: {TUMOR_CLASSES}")
    print("🌐 Accès: http://localhost:5000")
    print("🔬 Dashboard d'évolution: http://localhost:5000/evolution-dashboard")
    app.run(debug=True, host='0.0.0.0', port=5000)
