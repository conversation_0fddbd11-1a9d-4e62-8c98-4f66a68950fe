#!/usr/bin/env python3
"""
Test du modèle PyTorch pour NeuroScan
"""

import os
import sys

def test_pytorch_availability():
    """Tester la disponibilité de PyTorch"""
    print("🔍 Test de disponibilité PyTorch...")
    
    try:
        import torch
        print(f"✅ PyTorch version: {torch.__version__}")
        print(f"💻 CUDA disponible: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"🎯 GPU: {torch.cuda.get_device_name(0)}")
        
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🖥️ Device sélectionné: {device}")
        
        return True
    except ImportError as e:
        print(f"❌ PyTorch non disponible: {e}")
        return False

def test_model_file():
    """Tester la présence du fichier modèle"""
    print("\n🔍 Test du fichier modèle...")
    
    model_path = 'best_brain_tumor_model.pth'
    
    if os.path.exists(model_path):
        file_size = os.path.getsize(model_path)
        print(f"✅ Modèle trouvé: {model_path}")
        print(f"📊 Taille: {file_size / (1024*1024):.2f} MB")
        return True
    else:
        print(f"❌ Modèle non trouvé: {model_path}")
        return False

def test_model_loading():
    """Tester le chargement du modèle"""
    print("\n🔍 Test de chargement du modèle...")
    
    try:
        import torch
        import torch.nn as nn
        import torch.nn.functional as F
        
        # Définir l'architecture du modèle
        class BrainTumorCNN(nn.Module):
            def __init__(self, num_classes=4):
                super(BrainTumorCNN, self).__init__()
                
                # Couches de convolution
                self.conv1 = nn.Conv2d(3, 32, kernel_size=3, padding=1)
                self.conv2 = nn.Conv2d(32, 64, kernel_size=3, padding=1)
                self.conv3 = nn.Conv2d(64, 128, kernel_size=3, padding=1)
                self.conv4 = nn.Conv2d(128, 256, kernel_size=3, padding=1)
                self.conv5 = nn.Conv2d(256, 256, kernel_size=3, padding=1)
                
                # Couches de pooling et autres
                self.pool = nn.MaxPool2d(2, 2)
                self.relu = nn.ReLU()
                self.dropout = nn.Dropout(0.5)
                self.flatten = nn.Flatten()
                
                # Couches fully connected
                self.fc1 = nn.Linear(12544, 512)
                self.fc2 = nn.Linear(512, 4)
                
            def forward(self, x):
                # Appliquer les couches de convolution avec pooling
                x = self.pool(self.relu(self.conv1(x)))
                x = self.pool(self.relu(self.conv2(x)))
                x = self.pool(self.relu(self.conv3(x)))
                x = self.pool(self.relu(self.conv4(x)))
                x = self.pool(self.relu(self.conv5(x)))
                
                # Aplatir et appliquer les couches FC
                x = self.flatten(x)
                x = self.dropout(self.relu(self.fc1(x)))
                x = self.fc2(x)
                
                return x
        
        # Charger le modèle
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model = BrainTumorCNN(num_classes=4)
        
        try:
            model.load_state_dict(torch.load('best_brain_tumor_model.pth', map_location=device))
            model.to(device)
            model.eval()
            
            print(f"✅ Modèle chargé avec succès sur {device}")
            
            # Test avec un tensor aléatoire
            test_input = torch.randn(1, 3, 224, 224).to(device)
            with torch.no_grad():
                output = model(test_input)
                probabilities = F.softmax(output, dim=1)
                
            print(f"🧪 Test de prédiction réussi")
            print(f"📊 Shape de sortie: {output.shape}")
            print(f"🎯 Probabilités: {probabilities.cpu().numpy()[0]}")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors du chargement: {e}")
            return False
            
    except ImportError as e:
        print(f"❌ PyTorch non disponible pour le test: {e}")
        return False

def test_image_processing():
    """Tester le preprocessing d'images"""
    print("\n🔍 Test du preprocessing d'images...")
    
    try:
        from PIL import Image
        from torchvision import transforms
        import torch
        
        # Créer une image de test
        test_image = Image.new('RGB', (256, 256), color='red')
        
        # Définir les transformations
        transform = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        # Appliquer les transformations
        image_tensor = transform(test_image).unsqueeze(0)
        
        print(f"✅ Preprocessing réussi")
        print(f"📊 Shape du tensor: {image_tensor.shape}")
        print(f"🎯 Type: {image_tensor.dtype}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors du preprocessing: {e}")
        return False

def run_complete_test():
    """Exécuter tous les tests"""
    print("🧠 Test complet du système PyTorch NeuroScan")
    print("=" * 60)
    
    tests = [
        ("PyTorch disponibilité", test_pytorch_availability),
        ("Fichier modèle", test_model_file),
        ("Chargement modèle", test_model_loading),
        ("Preprocessing images", test_image_processing)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔬 {test_name}:")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur inattendue: {e}")
            results.append((test_name, False))
    
    # Résumé
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ DES TESTS")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ RÉUSSI" if result else "❌ ÉCHEC"
        print(f"{test_name:<25} : {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Tests réussis: {passed}/{len(results)}")
    
    if passed == len(results):
        print("🎉 Tous les tests sont réussis ! Le système PyTorch est opérationnel.")
        return True
    else:
        print("⚠️ Certains tests ont échoué. Vérifiez les erreurs ci-dessus.")
        return False

if __name__ == '__main__':
    success = run_complete_test()
    sys.exit(0 if success else 1)
