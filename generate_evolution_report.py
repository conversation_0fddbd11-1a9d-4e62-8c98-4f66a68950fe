#!/usr/bin/env python3
"""
Générateur de rapport d'évolution tumorale complet
"""

import sqlite3
import json
from datetime import datetime

DATABASE_PATH = 'neuroscan_analytics.db'

def generate_comprehensive_report(patient_id):
    """Générer un rapport d'évolution complet pour un patient"""
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()

    # Récupérer les informations du patient
    cursor.execute('SELECT * FROM patients WHERE patient_id = ?', (patient_id,))
    patient = cursor.fetchone()

    if not patient:
        print(f"❌ Patient {patient_id} non trouvé")
        return

    # Récupérer tous les examens
    cursor.execute('''
        SELECT * FROM examens_irm 
        WHERE patient_id = ? 
        ORDER BY exam_date ASC
    ''', (patient_id,))
    exams = cursor.fetchall()

    conn.close()

    if len(exams) < 2:
        print(f"⚠️ Pas assez d'examens pour générer un rapport d'évolution (trouvé: {len(exams)})")
        return

    # Générer le rapport
    print("=" * 80)
    print("🧠 RAPPORT D'ANALYSE IRM - ÉVOLUTION TUMORALE")
    print("=" * 80)
    print()

    # Informations patient
    print("👤 INFORMATIONS PATIENT")
    print("-" * 40)
    print(f"ID Patient      : {patient[1]}")
    print(f"Nom             : {patient[2]} {patient[3]}")
    print(f"Date naissance  : {patient[4] if patient[4] else 'Non renseignée'}")
    print(f"Sexe            : {patient[5] if patient[5] else 'Non renseigné'}")
    print(f"Médecin référent: {patient[6] if patient[6] else 'Non renseigné'}")
    print()

    # Résumé de l'évolution
    print("📊 RÉSUMÉ DE L'ÉVOLUTION")
    print("-" * 40)
    first_exam = exams[0]
    last_exam = exams[-1]
    
    first_date = datetime.fromisoformat(first_exam[2])
    last_date = datetime.fromisoformat(last_exam[2])
    total_days = (last_date - first_date).days

    print(f"Nombre d'examens : {len(exams)}")
    print(f"Période de suivi : {total_days} jours ({total_days/30.44:.1f} mois)")
    print(f"Premier examen   : {first_exam[2][:10]} - {first_exam[6]} ({first_exam[7]:.1%})")
    print(f"Dernier examen   : {last_exam[2][:10]} - {last_exam[6]} ({last_exam[7]:.1%})")
    
    # Déterminer la tendance générale
    first_probs = json.loads(first_exam[8])
    last_probs = json.loads(last_exam[8])
    
    print()
    print("🔍 ANALYSE COMPARATIVE GLOBALE")
    print("-" * 40)
    
    # Tableau des changements de probabilités
    prob_table = []
    for tumor_type in first_probs.keys():
        initial = first_probs[tumor_type]
        final = last_probs[tumor_type]
        change = final - initial
        change_percent = (change / initial * 100) if initial > 0 else 0
        
        trend = "↗" if change > 0.05 else "↘" if change < -0.05 else "→"
        
        prob_table.append([
            tumor_type,
            f"{initial:.1%}",
            f"{final:.1%}",
            f"{change:+.1%}",
            f"{change_percent:+.1f}%",
            trend
        ])
    
    # Affichage manuel du tableau
    print(f"{'Type de tumeur':<20} {'Initial':<8} {'Final':<8} {'Changement':<10} {'% Change':<10} {'Tendance':<8}")
    print("-" * 70)
    for row in prob_table:
        print(f"{row[0]:<20} {row[1]:<8} {row[2]:<8} {row[3]:<10} {row[4]:<10} {row[5]:<8}")
    print()

    # Chronologie détaillée des examens
    print("📅 CHRONOLOGIE DÉTAILLÉE DES EXAMENS")
    print("-" * 40)
    
    exam_table = []
    for i, exam in enumerate(exams):
        exam_date = exam[2][:10]
        diagnosis = exam[6]
        confidence = f"{exam[7]:.1%}"
        
        # Calculer l'intervalle depuis le précédent
        if i > 0:
            prev_date = datetime.fromisoformat(exams[i-1][2])
            curr_date = datetime.fromisoformat(exam[2])
            interval = (curr_date - prev_date).days
            interval_str = f"+{interval}j"
        else:
            interval_str = "Baseline"
        
        exam_table.append([
            f"Examen {i+1}",
            exam_date,
            interval_str,
            diagnosis,
            confidence
        ])
    
    # Affichage manuel du tableau
    print(f"{'Examen':<10} {'Date':<12} {'Intervalle':<10} {'Diagnostic':<15} {'Confiance':<10}")
    print("-" * 65)
    for row in exam_table:
        print(f"{row[0]:<10} {row[1]:<12} {row[2]:<10} {row[3]:<15} {row[4]:<10}")
    print()

    # Analyse d'évolution entre examens consécutifs
    print("🔬 ANALYSE D'ÉVOLUTION DÉTAILLÉE")
    print("-" * 40)
    
    for i in range(1, len(exams)):
        previous_exam = exams[i-1]
        current_exam = exams[i]
        
        print(f"\n📋 Évolution {i}: Examen {i} → Examen {i+1}")
        print(f"   Période: {previous_exam[2][:10]} → {current_exam[2][:10]}")
        
        # Calculer les métriques d'évolution
        evolution = calculate_evolution_metrics_simple(previous_exam, current_exam)
        
        if evolution:
            print(f"   Type d'évolution: {evolution['evolution_type'].upper()}")
            print(f"   Niveau d'alerte: {evolution['alert_level'].upper()}")
            print(f"   Intervalle: {evolution['time_interval_days']} jours")
            
            # Afficher l'alerte si nécessaire
            if evolution['alert_level'] in ['critical', 'high']:
                alert_symbol = "🚨" if evolution['alert_level'] == 'critical' else "⚠️"
                print(f"   {alert_symbol} ALERTE {evolution['alert_level'].upper()}")
            
            # Changements significatifs
            significant_changes = []
            for tumor_type, change_data in evolution['probability_changes'].items():
                if abs(change_data['change']) > 0.1:
                    direction = "↗" if change_data['change'] > 0 else "↘"
                    significant_changes.append(f"{tumor_type} {direction} {abs(change_data['change']):.1%}")
            
            if significant_changes:
                print(f"   Changements: {', '.join(significant_changes)}")

    # Recommandations finales
    print()
    print("💡 RECOMMANDATIONS MÉDICALES")
    print("-" * 40)
    
    # Analyser la tendance générale
    if first_exam[6] != last_exam[6]:
        print("🔴 PRIORITÉ ÉLEVÉE:")
        print("   • Changement de diagnostic détecté")
        print("   • Réévaluation clinique urgente recommandée")
        print("   • Consultation spécialisée dans les 48h")
    else:
        # Analyser les changements de probabilités
        max_change = max([abs(last_probs[t] - first_probs[t]) for t in first_probs.keys()])
        
        if max_change > 0.3:
            print("🟡 SURVEILLANCE RENFORCÉE:")
            print("   • Changements significatifs détectés")
            print("   • Suivi rapproché recommandé (1-2 mois)")
        elif max_change > 0.1:
            print("🟢 SUIVI STANDARD:")
            print("   • Évolution modérée observée")
            print("   • Maintenir le protocole de suivi habituel")
        else:
            print("🟢 ÉVOLUTION STABLE:")
            print("   • Pas de changement significatif")
            print("   • Suivi de routine approprié")

    print()
    print("⚠️ AVERTISSEMENT MÉDICAL")
    print("-" * 40)
    print("Ce rapport est généré par un système d'IA d'aide au diagnostic.")
    print("Il ne remplace pas l'expertise médicale et doit être interprété")
    print("par un professionnel de santé qualifié.")
    print()
    print("=" * 80)

def calculate_evolution_metrics_simple(previous_exam, current_exam):
    """Version simplifiée du calcul d'évolution pour le rapport"""
    try:
        prev_probs = json.loads(previous_exam[8])
        curr_probs = json.loads(current_exam[8])
        
        prev_date = datetime.fromisoformat(previous_exam[2])
        curr_date = datetime.fromisoformat(current_exam[2])
        time_interval = (curr_date - prev_date).days
        
        prob_changes = {}
        for tumor_type in prev_probs.keys():
            change = curr_probs[tumor_type] - prev_probs[tumor_type]
            prob_changes[tumor_type] = {
                'previous': prev_probs[tumor_type],
                'current': curr_probs[tumor_type],
                'change': change
            }
        
        # Déterminer le type d'évolution
        prev_label = previous_exam[6]
        curr_label = current_exam[6]
        
        if prev_label != curr_label:
            evolution_type = 'changement_type'
            alert_level = 'critical' if 'Gliome' in curr_label else 'high'
        else:
            current_prob_change = prob_changes[curr_label]['change']
            if abs(current_prob_change) < 0.05:
                evolution_type = 'stable'
                alert_level = 'none'
            elif current_prob_change > 0:
                evolution_type = 'progression'
                alert_level = 'medium' if current_prob_change > 0.2 else 'low'
            else:
                evolution_type = 'regression'
                alert_level = 'low'
        
        return {
            'evolution_type': evolution_type,
            'alert_level': alert_level,
            'time_interval_days': time_interval,
            'probability_changes': prob_changes
        }
        
    except Exception as e:
        print(f"Erreur: {e}")
        return None

if __name__ == '__main__':
    print("🧠 Générateur de rapport d'évolution tumorale")
    print()
    
    # Générer le rapport pour PAT001
    generate_comprehensive_report('PAT001')
