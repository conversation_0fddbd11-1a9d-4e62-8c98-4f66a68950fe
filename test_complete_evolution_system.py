#!/usr/bin/env python3
"""
Test complet du système d'analyse d'évolution tumorale NeuroScan
"""

import sqlite3
import json
from datetime import datetime, timedelta
import random

DATABASE_PATH = 'neuroscan_analytics.db'

def create_comprehensive_test_data():
    """Créer un jeu de données complet pour tester tous les scénarios"""
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()

    print("🧪 Création de données de test complètes...")

    # Scénario 1: Évolution progressive vers gliome (PAT100)
    create_patient(cursor, 'PAT100', 'Progression', 'Marie', '1970-05-15', 'F', 'Dr. Neurologie')
    
    base_date = datetime.now() - timedelta(days=365)
    
    # Examen 1: Normal
    create_exam(cursor, 'PAT100', base_date, 'Normal', 0.90, 
                {'Normal': 0.90, 'Gliome': 0.05, 'Méningiome': 0.03, 'Tumeur pituitaire': 0.02})
    
    # Examen 2: Suspicion (6 mois plus tard)
    create_exam(cursor, 'PAT100', base_date + timedelta(days=180), 'Normal', 0.70,
                {'Normal': 0.70, 'Gliome': 0.20, 'Méningiome': 0.07, 'Tumeur pituitaire': 0.03})
    
    # Examen 3: Confirmation gliome (3 mois plus tard)
    create_exam(cursor, 'PAT100', base_date + timedelta(days=270), 'Gliome', 0.85,
                {'Normal': 0.10, 'Gliome': 0.85, 'Méningiome': 0.03, 'Tumeur pituitaire': 0.02})
    
    # Examen 4: Progression (2 mois plus tard)
    create_exam(cursor, 'PAT100', base_date + timedelta(days=330), 'Gliome', 0.92,
                {'Normal': 0.05, 'Gliome': 0.92, 'Méningiome': 0.02, 'Tumeur pituitaire': 0.01})

    # Scénario 2: Évolution stable méningiome (PAT200)
    create_patient(cursor, 'PAT200', 'Stable', 'Jean', '1965-08-22', 'M', 'Dr. Neurochirurgie')
    
    stable_probs = {'Normal': 0.05, 'Gliome': 0.05, 'Méningiome': 0.85, 'Tumeur pituitaire': 0.05}
    for i in range(4):
        exam_date = base_date + timedelta(days=i*90)
        # Légères variations pour simuler la stabilité
        varied_probs = {k: v + random.uniform(-0.03, 0.03) for k, v in stable_probs.items()}
        varied_probs = {k: max(0.01, min(0.98, v)) for k, v in varied_probs.items()}
        create_exam(cursor, 'PAT200', exam_date, 'Méningiome', 0.85 + random.uniform(-0.05, 0.05), varied_probs)

    # Scénario 3: Régression (PAT300)
    create_patient(cursor, 'PAT300', 'Regression', 'Sophie', '1980-12-03', 'F', 'Dr. Oncologie')
    
    # Examen 1: Gliome suspecté
    create_exam(cursor, 'PAT300', base_date, 'Gliome', 0.75,
                {'Normal': 0.15, 'Gliome': 0.75, 'Méningiome': 0.08, 'Tumeur pituitaire': 0.02})
    
    # Examen 2: Amélioration (4 mois plus tard)
    create_exam(cursor, 'PAT300', base_date + timedelta(days=120), 'Normal', 0.80,
                {'Normal': 0.80, 'Gliome': 0.12, 'Méningiome': 0.06, 'Tumeur pituitaire': 0.02})

    # Scénario 4: Changement de type (PAT400)
    create_patient(cursor, 'PAT400', 'Changement', 'Pierre', '1975-03-10', 'M', 'Dr. Radiologie')
    
    # Examen 1: Méningiome
    create_exam(cursor, 'PAT400', base_date, 'Méningiome', 0.80,
                {'Normal': 0.10, 'Gliome': 0.05, 'Méningiome': 0.80, 'Tumeur pituitaire': 0.05})
    
    # Examen 2: Changement vers tumeur pituitaire
    create_exam(cursor, 'PAT400', base_date + timedelta(days=150), 'Tumeur pituitaire', 0.85,
                {'Normal': 0.05, 'Gliome': 0.05, 'Méningiome': 0.05, 'Tumeur pituitaire': 0.85})

    # Scénario 5: Évolution rapide critique (PAT500)
    create_patient(cursor, 'PAT500', 'Critique', 'Anna', '1985-07-18', 'F', 'Dr. Urgences')
    
    # Examen 1: Normal
    create_exam(cursor, 'PAT500', base_date + timedelta(days=300), 'Normal', 0.95,
                {'Normal': 0.95, 'Gliome': 0.02, 'Méningiome': 0.02, 'Tumeur pituitaire': 0.01})
    
    # Examen 2: Gliome en 30 jours (évolution très rapide)
    create_exam(cursor, 'PAT500', base_date + timedelta(days=330), 'Gliome', 0.90,
                {'Normal': 0.05, 'Gliome': 0.90, 'Méningiome': 0.03, 'Tumeur pituitaire': 0.02})

    conn.commit()
    conn.close()
    print("✅ Données de test créées avec succès!")

def create_patient(cursor, patient_id, nom, prenom, date_naissance, sexe, medecin):
    """Créer un patient de test"""
    cursor.execute('''
        INSERT OR IGNORE INTO patients (patient_id, nom, prenom, date_naissance, sexe, medecin_referent)
        VALUES (?, ?, ?, ?, ?, ?)
    ''', (patient_id, nom, prenom, date_naissance, sexe, medecin))

def create_exam(cursor, patient_id, exam_date, predicted_label, confidence, probabilities):
    """Créer un examen de test"""
    predicted_class = {'Normal': 0, 'Gliome': 1, 'Méningiome': 2, 'Tumeur pituitaire': 3}[predicted_label]
    
    cursor.execute('''
        INSERT INTO examens_irm 
        (patient_id, exam_date, filename, predicted_class, predicted_label, confidence, probabilities, description)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ''', (patient_id, exam_date, f'{patient_id}_{exam_date.strftime("%Y%m%d")}.jpg', 
          predicted_class, predicted_label, confidence, json.dumps(probabilities), 
          f'Examen de test - {predicted_label}'))

def test_evolution_analysis():
    """Tester l'analyse d'évolution pour tous les patients"""
    print("\n🔍 Test de l'analyse d'évolution...")
    
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()
    
    # Récupérer tous les patients de test
    test_patients = ['PAT100', 'PAT200', 'PAT300', 'PAT400', 'PAT500']
    
    for patient_id in test_patients:
        print(f"\n📊 Patient {patient_id}:")
        
        # Récupérer les examens
        cursor.execute('''
            SELECT * FROM examens_irm 
            WHERE patient_id = ? 
            ORDER BY exam_date ASC
        ''', (patient_id,))
        exams = cursor.fetchall()
        
        print(f"   Nombre d'examens: {len(exams)}")
        
        if len(exams) >= 2:
            # Analyser l'évolution entre le premier et le dernier examen
            first_exam = exams[0]
            last_exam = exams[-1]
            
            first_date = datetime.fromisoformat(first_exam[2])
            last_date = datetime.fromisoformat(last_exam[2])
            interval = (last_date - first_date).days
            
            print(f"   Période: {interval} jours")
            print(f"   Évolution: {first_exam[6]} → {last_exam[6]}")
            print(f"   Confiance: {first_exam[7]:.1%} → {last_exam[7]:.1%}")
            
            # Calculer les changements de probabilités
            first_probs = json.loads(first_exam[8])
            last_probs = json.loads(last_exam[8])
            
            max_change = 0
            max_change_type = None
            for tumor_type in first_probs.keys():
                change = last_probs[tumor_type] - first_probs[tumor_type]
                if abs(change) > abs(max_change):
                    max_change = change
                    max_change_type = tumor_type
            
            print(f"   Plus grand changement: {max_change_type} ({max_change:+.1%})")
            
            # Déterminer le niveau d'alerte
            if first_exam[6] != last_exam[6]:
                if 'Gliome' in last_exam[6]:
                    alert = "🚨 CRITIQUE"
                else:
                    alert = "⚠️ ÉLEVÉ"
            elif abs(max_change) > 0.3:
                alert = "⚠️ MOYEN"
            elif abs(max_change) > 0.1:
                alert = "📊 FAIBLE"
            else:
                alert = "✅ NORMAL"
            
            print(f"   Niveau d'alerte: {alert}")
        else:
            print("   ⚠️ Pas assez d'examens pour l'analyse d'évolution")
    
    conn.close()

def test_report_generation():
    """Tester la génération de rapports"""
    print("\n📋 Test de génération de rapports...")
    
    try:
        from evolution_report_generator import generate_html_evolution_report
        
        test_patients = ['PAT100', 'PAT200', 'PAT300']
        
        for patient_id in test_patients:
            print(f"   Génération du rapport pour {patient_id}...")
            report_file = generate_html_evolution_report(patient_id)
            
            if report_file:
                import os
                file_size = os.path.getsize(report_file)
                print(f"   ✅ Rapport généré: {report_file} ({file_size} bytes)")
                # Nettoyer le fichier de test
                os.remove(report_file)
            else:
                print(f"   ❌ Erreur lors de la génération du rapport pour {patient_id}")
    
    except ImportError:
        print("   ⚠️ Module de génération de rapports non disponible")

def test_database_integrity():
    """Tester l'intégrité de la base de données"""
    print("\n🗄️ Test d'intégrité de la base de données...")
    
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()
    
    # Vérifier les tables
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = [row[0] for row in cursor.fetchall()]
    
    required_tables = ['patients', 'examens_irm', 'evolution_analysis']
    missing_tables = [table for table in required_tables if table not in tables]
    
    if missing_tables:
        print(f"   ❌ Tables manquantes: {missing_tables}")
    else:
        print("   ✅ Toutes les tables requises sont présentes")
    
    # Vérifier les données
    cursor.execute("SELECT COUNT(*) FROM patients")
    patient_count = cursor.fetchone()[0]
    
    cursor.execute("SELECT COUNT(*) FROM examens_irm")
    exam_count = cursor.fetchone()[0]
    
    print(f"   📊 Patients: {patient_count}")
    print(f"   📊 Examens: {exam_count}")
    
    # Vérifier les contraintes de clés étrangères
    cursor.execute('''
        SELECT COUNT(*) FROM examens_irm e
        LEFT JOIN patients p ON e.patient_id = p.patient_id
        WHERE p.patient_id IS NULL
    ''')
    orphaned_exams = cursor.fetchone()[0]
    
    if orphaned_exams > 0:
        print(f"   ⚠️ {orphaned_exams} examens sans patient associé")
    else:
        print("   ✅ Intégrité référentielle respectée")
    
    conn.close()

def generate_test_summary():
    """Générer un résumé des tests"""
    print("\n📈 Résumé des tests d'évolution tumorale")
    print("=" * 50)
    
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()
    
    # Statistiques par type d'évolution
    cursor.execute('''
        SELECT 
            p.patient_id,
            p.nom,
            COUNT(e.id) as total_exams,
            MIN(e.exam_date) as first_exam,
            MAX(e.exam_date) as last_exam,
            (SELECT predicted_label FROM examens_irm WHERE patient_id = p.patient_id ORDER BY exam_date ASC LIMIT 1) as first_diagnosis,
            (SELECT predicted_label FROM examens_irm WHERE patient_id = p.patient_id ORDER BY exam_date DESC LIMIT 1) as last_diagnosis
        FROM patients p
        JOIN examens_irm e ON p.patient_id = e.patient_id
        WHERE p.patient_id LIKE 'PAT%'
        GROUP BY p.patient_id, p.nom
        ORDER BY p.patient_id
    ''')
    
    results = cursor.fetchall()
    
    print(f"{'Patient':<10} {'Nom':<12} {'Examens':<8} {'Évolution':<25} {'Durée':<10}")
    print("-" * 75)
    
    for result in results:
        patient_id, nom, total_exams, first_exam, last_exam, first_diag, last_diag = result
        
        first_date = datetime.fromisoformat(first_exam)
        last_date = datetime.fromisoformat(last_exam)
        duration = (last_date - first_date).days
        
        evolution = f"{first_diag} → {last_diag}" if first_diag != last_diag else f"{first_diag} (stable)"
        
        print(f"{patient_id:<10} {nom:<12} {total_exams:<8} {evolution:<25} {duration}j")
    
    conn.close()
    
    print("\n✅ Tests terminés avec succès!")
    print("🎯 Le système d'analyse d'évolution tumorale est opérationnel!")

if __name__ == '__main__':
    print("🧠 Test complet du système d'évolution tumorale NeuroScan")
    print("=" * 60)
    
    # Créer les données de test
    create_comprehensive_test_data()
    
    # Tester l'intégrité de la base de données
    test_database_integrity()
    
    # Tester l'analyse d'évolution
    test_evolution_analysis()
    
    # Tester la génération de rapports
    test_report_generation()
    
    # Générer le résumé
    generate_test_summary()
