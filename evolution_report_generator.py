#!/usr/bin/env python3
"""
Générateur de rapports d'évolution tumorale avec export HTML/PDF
"""

import sqlite3
import json
from datetime import datetime
import base64
import os

DATABASE_PATH = 'neuroscan_analytics.db'

def generate_html_evolution_report(patient_id, output_file=None):
    """Générer un rapport d'évolution en format HTML"""
    
    # Récupérer les données du patient
    data = get_patient_evolution_data(patient_id)
    if not data:
        return None
    
    # Générer le HTML
    html_content = create_html_report(data)
    
    # Sauvegarder le fichier
    if output_file is None:
        output_file = f"rapport_evolution_{patient_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    return output_file

def get_patient_evolution_data(patient_id):
    """<PERSON><PERSON><PERSON><PERSON><PERSON> toutes les données nécessaires pour le rapport"""
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()

    # Informations patient
    cursor.execute('SELECT * FROM patients WHERE patient_id = ?', (patient_id,))
    patient = cursor.fetchone()
    
    if not patient:
        conn.close()
        return None

    # Examens
    cursor.execute('''
        SELECT * FROM examens_irm 
        WHERE patient_id = ? 
        ORDER BY exam_date ASC
    ''', (patient_id,))
    exams = cursor.fetchall()

    # Analyses d'évolution
    cursor.execute('''
        SELECT * FROM evolution_analysis 
        WHERE patient_id = ? 
        ORDER BY created_at ASC
    ''', (patient_id,))
    evolutions = cursor.fetchall()

    conn.close()

    return {
        'patient': patient,
        'exams': exams,
        'evolutions': evolutions
    }

def create_html_report(data):
    """Créer le contenu HTML du rapport"""
    
    patient = data['patient']
    exams = data['exams']
    evolutions = data['evolutions']
    
    # Calculer les métriques de base
    if len(exams) < 2:
        return create_single_exam_html(patient, exams[0] if exams else None)
    
    first_exam = exams[0]
    last_exam = exams[-1]
    
    first_date = datetime.fromisoformat(first_exam[2])
    last_date = datetime.fromisoformat(last_exam[2])
    total_days = (last_date - first_date).days
    
    # Analyser l'évolution globale
    first_probs = json.loads(first_exam[8]) if first_exam[8] else {}
    last_probs = json.loads(last_exam[8]) if last_exam[8] else {}
    
    html = f"""
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rapport d'Évolution Tumorale - {patient[1]}</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }}
        .header h1 {{
            color: #2c3e50;
            margin: 0;
            font-size: 2.5em;
        }}
        .header .subtitle {{
            color: #7f8c8d;
            font-size: 1.2em;
            margin-top: 10px;
        }}
        .section {{
            margin-bottom: 30px;
            padding: 20px;
            border-left: 4px solid #3498db;
            background-color: #f8f9fa;
        }}
        .section h2 {{
            color: #2c3e50;
            margin-top: 0;
            display: flex;
            align-items: center;
        }}
        .section h2::before {{
            content: "📊";
            margin-right: 10px;
            font-size: 1.2em;
        }}
        .patient-info {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }}
        .info-item {{
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }}
        .info-label {{
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }}
        .info-value {{
            color: #34495e;
            font-size: 1.1em;
        }}
        .evolution-table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .evolution-table th {{
            background: #3498db;
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
        }}
        .evolution-table td {{
            padding: 12px 15px;
            border-bottom: 1px solid #ecf0f1;
        }}
        .evolution-table tr:hover {{
            background-color: #f8f9fa;
        }}
        .alert-critical {{
            background-color: #e74c3c;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9em;
            font-weight: bold;
        }}
        .alert-high {{
            background-color: #f39c12;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9em;
            font-weight: bold;
        }}
        .alert-medium {{
            background-color: #f1c40f;
            color: #2c3e50;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9em;
            font-weight: bold;
        }}
        .alert-low {{
            background-color: #2ecc71;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9em;
            font-weight: bold;
        }}
        .alert-none {{
            background-color: #95a5a6;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9em;
            font-weight: bold;
        }}
        .probability-bar {{
            height: 20px;
            background: #ecf0f1;
            border-radius: 10px;
            overflow: hidden;
            margin: 5px 0;
        }}
        .probability-fill {{
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2980b9);
            transition: width 0.3s ease;
        }}
        .recommendations {{
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }}
        .recommendations h3 {{
            color: #856404;
            margin-top: 0;
        }}
        .recommendations ul {{
            margin: 0;
            padding-left: 20px;
        }}
        .recommendations li {{
            margin-bottom: 8px;
            color: #856404;
        }}
        .footer {{
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #ecf0f1;
            color: #7f8c8d;
            font-size: 0.9em;
        }}
        .chart-container {{
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        @media print {{
            body {{ background: white; }}
            .container {{ box-shadow: none; }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 Rapport d'Évolution Tumorale</h1>
            <div class="subtitle">Analyse comparative IRM - NeuroScan AI</div>
            <div class="subtitle">Généré le {datetime.now().strftime('%d/%m/%Y à %H:%M')}</div>
        </div>

        <div class="section">
            <h2>Informations Patient</h2>
            <div class="patient-info">
                <div class="info-item">
                    <div class="info-label">ID Patient</div>
                    <div class="info-value">{patient[1]}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Nom Complet</div>
                    <div class="info-value">{patient[2]} {patient[3]}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Date de Naissance</div>
                    <div class="info-value">{patient[4] if patient[4] else 'Non renseignée'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Sexe</div>
                    <div class="info-value">{patient[5] if patient[5] else 'Non renseigné'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Médecin Référent</div>
                    <div class="info-value">{patient[6] if patient[6] else 'Non renseigné'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Période de Suivi</div>
                    <div class="info-value">{total_days} jours ({total_days/30.44:.1f} mois)</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>Résumé de l'Évolution</h2>
            <div class="patient-info">
                <div class="info-item">
                    <div class="info-label">Nombre d'Examens</div>
                    <div class="info-value">{len(exams)}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Premier Diagnostic</div>
                    <div class="info-value">{first_exam[6]} ({first_exam[7]:.1%})</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Dernier Diagnostic</div>
                    <div class="info-value">{last_exam[6]} ({last_exam[7]:.1%})</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Changement de Diagnostic</div>
                    <div class="info-value">{'Oui' if first_exam[6] != last_exam[6] else 'Non'}</div>
                </div>
            </div>
        </div>
"""
    
    # Ajouter le tableau des examens
    html += create_exams_table_html(exams)
    
    # Ajouter l'analyse des probabilités
    html += create_probability_analysis_html(first_probs, last_probs)
    
    # Ajouter les recommandations
    html += create_recommendations_html(first_exam, last_exam, evolutions)
    
    # Footer
    html += f"""
        <div class="footer">
            <p><strong>⚠️ Avertissement Médical</strong></p>
            <p>Ce rapport est généré par un système d'IA d'aide au diagnostic NeuroScan.<br>
            Il ne remplace pas l'expertise médicale et doit être interprété par un professionnel de santé qualifié.</p>
            <p>Rapport généré le {datetime.now().strftime('%d/%m/%Y à %H:%M:%S')}</p>
        </div>
    </div>
</body>
</html>
"""
    
    return html

def create_exams_table_html(exams):
    """Créer le tableau HTML des examens"""
    html = """
        <div class="section">
            <h2>Chronologie des Examens</h2>
            <table class="evolution-table">
                <thead>
                    <tr>
                        <th>Examen</th>
                        <th>Date</th>
                        <th>Intervalle</th>
                        <th>Diagnostic</th>
                        <th>Confiance</th>
                        <th>Probabilités</th>
                    </tr>
                </thead>
                <tbody>
    """
    
    for i, exam in enumerate(exams):
        # Calculer l'intervalle
        if i > 0:
            prev_date = datetime.fromisoformat(exams[i-1][2])
            curr_date = datetime.fromisoformat(exam[2])
            interval = (curr_date - prev_date).days
            interval_str = f"+{interval}j"
        else:
            interval_str = "Baseline"
        
        # Parser les probabilités
        probs = json.loads(exam[8]) if exam[8] else {}
        prob_html = ""
        for tumor_type, prob in probs.items():
            prob_html += f"<div>{tumor_type}: {prob:.1%}</div>"
        
        html += f"""
                    <tr>
                        <td>Examen {i+1}</td>
                        <td>{exam[2][:10]}</td>
                        <td>{interval_str}</td>
                        <td><strong>{exam[6]}</strong></td>
                        <td>{exam[7]:.1%}</td>
                        <td>{prob_html}</td>
                    </tr>
        """
    
    html += """
                </tbody>
            </table>
        </div>
    """
    
    return html

def create_probability_analysis_html(first_probs, last_probs):
    """Créer l'analyse HTML des changements de probabilités"""
    html = """
        <div class="section">
            <h2>Analyse des Changements de Probabilités</h2>
            <table class="evolution-table">
                <thead>
                    <tr>
                        <th>Type de Tumeur</th>
                        <th>Probabilité Initiale</th>
                        <th>Probabilité Finale</th>
                        <th>Changement</th>
                        <th>Évolution</th>
                    </tr>
                </thead>
                <tbody>
    """
    
    for tumor_type in first_probs.keys():
        initial = first_probs[tumor_type]
        final = last_probs.get(tumor_type, 0)
        change = final - initial
        change_percent = (change / initial * 100) if initial > 0 else 0
        
        # Déterminer la tendance
        if abs(change) < 0.05:
            trend = "→ Stable"
            trend_class = "alert-none"
        elif change > 0:
            trend = "↗ Augmentation"
            trend_class = "alert-high" if change > 0.3 else "alert-medium"
        else:
            trend = "↘ Diminution"
            trend_class = "alert-low"
        
        html += f"""
                    <tr>
                        <td><strong>{tumor_type}</strong></td>
                        <td>{initial:.1%}</td>
                        <td>{final:.1%}</td>
                        <td>{change:+.1%} ({change_percent:+.1f}%)</td>
                        <td><span class="{trend_class}">{trend}</span></td>
                    </tr>
        """
    
    html += """
                </tbody>
            </table>
        </div>
    """
    
    return html

def create_recommendations_html(first_exam, last_exam, evolutions):
    """Créer les recommandations HTML"""
    recommendations = []
    
    # Analyser le changement de diagnostic
    if first_exam[6] != last_exam[6]:
        recommendations.append("🔴 PRIORITÉ ÉLEVÉE: Changement de diagnostic détecté")
        recommendations.append("⚠️ Réévaluation clinique urgente recommandée")
        recommendations.append("🏥 Consultation spécialisée dans les 48h")
    
    # Analyser les alertes d'évolution
    critical_alerts = sum(1 for ev in evolutions if len(ev) > 9 and ev[9] == 'critical')
    high_alerts = sum(1 for ev in evolutions if len(ev) > 9 and ev[9] == 'high')
    
    if critical_alerts > 0:
        recommendations.append(f"🚨 {critical_alerts} alerte(s) critique(s) détectée(s)")
    if high_alerts > 0:
        recommendations.append(f"⚠️ {high_alerts} alerte(s) importante(s) détectée(s)")
    
    if not recommendations:
        recommendations.append("✅ Évolution dans les paramètres normaux")
        recommendations.append("📊 Maintenir le protocole de suivi habituel")
    
    html = f"""
        <div class="recommendations">
            <h3>💡 Recommandations Médicales</h3>
            <ul>
    """
    
    for rec in recommendations:
        html += f"<li>{rec}</li>"
    
    html += """
            </ul>
        </div>
    """
    
    return html

def create_single_exam_html(patient, exam):
    """Créer un rapport HTML pour un seul examen"""
    return f"""
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Rapport Unique - {patient[1] if patient else 'Patient'}</title>
</head>
<body>
    <h1>Rapport d'Analyse Unique</h1>
    <p>Ce patient n'a qu'un seul examen. L'analyse d'évolution nécessite au moins deux examens.</p>
    {f'<p>Diagnostic: {exam[6]} (Confiance: {exam[7]:.1%})</p>' if exam else '<p>Aucun examen trouvé.</p>'}
</body>
</html>
"""

if __name__ == '__main__':
    print("🧠 Générateur de rapports d'évolution HTML")
    print("=" * 50)
    
    # Générer le rapport pour PAT001
    output_file = generate_html_evolution_report('PAT001')
    
    if output_file:
        print(f"✅ Rapport généré: {output_file}")
        print(f"📂 Taille du fichier: {os.path.getsize(output_file)} bytes")
    else:
        print("❌ Erreur lors de la génération du rapport")
