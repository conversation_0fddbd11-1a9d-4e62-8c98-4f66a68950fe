from flask import Flask, render_template, request, jsonify, send_from_directory, Response
import torch
import torch.nn as nn
import torch.nn.functional as F
from PIL import Image
import numpy as np
import io
import base64
import os
import time
import requests
import json
import sqlite3
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from werkzeug.utils import secure_filename
import cv2
from torchvision import transforms

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
app.config['UPLOAD_FOLDER'] = 'uploads'

# Créer le dossier uploads s'il n'existe pas
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Configuration de l'API Gemini
GEMINI_API_KEY = "AIzaSyBC3sAJjh9_32jTgKXJxcdOTM7HzyNJPng"
GEMINI_API_URL = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent"

# Configuration de la base de données
DATABASE_PATH = 'neuroscan_analytics.db'

def init_database():
    """Initialiser la base de données avec les tables nécessaires"""
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()

    # Table des analyses (existante - maintenue pour compatibilité)
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS analyses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            filename TEXT,
            predicted_class INTEGER,
            predicted_label TEXT,
            confidence REAL,
            probabilities TEXT,
            description TEXT,
            recommendations TEXT,
            processing_time REAL,
            user_session TEXT,
            ip_address TEXT
        )
    ''')

    # Table des statistiques quotidiennes
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS daily_stats (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date DATE UNIQUE,
            total_analyses INTEGER DEFAULT 0,
            normal_count INTEGER DEFAULT 0,
            gliome_count INTEGER DEFAULT 0,
            meningiome_count INTEGER DEFAULT 0,
            pituitary_count INTEGER DEFAULT 0,
            avg_confidence REAL DEFAULT 0,
            avg_processing_time REAL DEFAULT 0
        )
    ''')

    # Table des sessions utilisateur
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS user_sessions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            session_id TEXT UNIQUE,
            start_time DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
            analyses_count INTEGER DEFAULT 0,
            ip_address TEXT,
            user_agent TEXT
        )
    ''')

    # ========== NOUVELLES TABLES POUR L'ANALYSE D'ÉVOLUTION TUMORALE ==========

    # Table des patients
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS patients (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            patient_id TEXT UNIQUE NOT NULL,
            nom TEXT,
            prenom TEXT,
            date_naissance DATE,
            sexe TEXT CHECK(sexe IN ('M', 'F', 'Autre')),
            medecin_referent TEXT,
            notes_medicales TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Table des examens IRM (nouvelle structure pour le suivi temporel)
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS examens_irm (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            patient_id TEXT NOT NULL,
            exam_date DATETIME NOT NULL,
            filename TEXT,
            image_metadata TEXT,
            predicted_class INTEGER,
            predicted_label TEXT,
            confidence REAL,
            probabilities TEXT,
            description TEXT,
            recommendations TEXT,
            processing_time REAL,
            user_session TEXT,
            ip_address TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (patient_id) REFERENCES patients(patient_id)
        )
    ''')

    # Table d'analyse comparative d'évolution
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS evolution_analysis (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            patient_id TEXT NOT NULL,
            exam_id_previous INTEGER,
            exam_id_current INTEGER,
            time_interval_days INTEGER,
            evolution_type TEXT CHECK(evolution_type IN ('stable', 'progression', 'regression', 'changement_type')),
            confidence_change REAL,
            probability_changes TEXT,
            volume_change_estimate TEXT,
            alert_level TEXT CHECK(alert_level IN ('none', 'low', 'medium', 'high', 'critical')),
            medical_interpretation TEXT,
            recommendations TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (patient_id) REFERENCES patients(patient_id),
            FOREIGN KEY (exam_id_previous) REFERENCES examens_irm(id),
            FOREIGN KEY (exam_id_current) REFERENCES examens_irm(id)
        )
    ''')

    # Index pour optimiser les requêtes temporelles
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_patient_exam_date ON examens_irm(patient_id, exam_date)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_evolution_patient ON evolution_analysis(patient_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_evolution_alert ON evolution_analysis(alert_level)')

    conn.commit()
    conn.close()

def save_analysis_to_db(results, filename, processing_time, session_id=None, ip_address=None):
    """Sauvegarder une analyse dans la base de données"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        # Convertir les probabilités en JSON
        probabilities_json = json.dumps(results['probabilities'])
        recommendations_json = json.dumps(results.get('recommendations', []))

        cursor.execute('''
            INSERT INTO analyses
            (filename, predicted_class, predicted_label, confidence, probabilities,
             description, recommendations, processing_time, user_session, ip_address)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            filename,
            results['predicted_class'],
            results['predicted_label'],
            results['confidence'],
            probabilities_json,
            results.get('description', ''),
            recommendations_json,
            processing_time,
            session_id,
            ip_address
        ))

        # Mettre à jour les statistiques quotidiennes
        today = datetime.now().date()
        cursor.execute('''
            INSERT OR IGNORE INTO daily_stats (date) VALUES (?)
        ''', (today,))

        # Incrémenter les compteurs
        label_column = {
            'Normal': 'normal_count',
            'Gliome': 'gliome_count',
            'Méningiome': 'meningiome_count',
            'Tumeur pituitaire': 'pituitary_count'
        }.get(results['predicted_label'], 'normal_count')

        cursor.execute(f'''
            UPDATE daily_stats
            SET total_analyses = total_analyses + 1,
                {label_column} = {label_column} + 1,
                avg_confidence = (avg_confidence * (total_analyses - 1) + ?) / total_analyses,
                avg_processing_time = (avg_processing_time * (total_analyses - 1) + ?) / total_analyses
            WHERE date = ?
        ''', (results['confidence'], processing_time, today))

        conn.commit()
        conn.close()
        return True
    except Exception as e:
        print(f"Erreur lors de la sauvegarde: {e}")
        return False

# ========== NOUVELLES FONCTIONS POUR LA GESTION DES PATIENTS ET EXAMENS ==========

def create_or_get_patient(patient_id, nom=None, prenom=None, date_naissance=None, sexe=None, medecin_referent=None):
    """Créer un nouveau patient ou récupérer un patient existant"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        # Vérifier si le patient existe déjà
        cursor.execute('SELECT * FROM patients WHERE patient_id = ?', (patient_id,))
        existing_patient = cursor.fetchone()

        if existing_patient:
            # Mettre à jour la date de dernière modification
            cursor.execute('''
                UPDATE patients SET updated_at = CURRENT_TIMESTAMP WHERE patient_id = ?
            ''', (patient_id,))
            conn.commit()
            conn.close()
            return existing_patient[0]  # Retourner l'ID
        else:
            # Créer un nouveau patient
            cursor.execute('''
                INSERT INTO patients (patient_id, nom, prenom, date_naissance, sexe, medecin_referent)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (patient_id, nom, prenom, date_naissance, sexe, medecin_referent))

            patient_db_id = cursor.lastrowid
            conn.commit()
            conn.close()
            return patient_db_id

    except Exception as e:
        print(f"Erreur lors de la création/récupération du patient: {e}")
        return None

def save_exam_to_db(patient_id, results, filename, processing_time, exam_date=None,
                   image_metadata=None, session_id=None, ip_address=None):
    """Sauvegarder un examen IRM dans la nouvelle structure"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        # Utiliser la date actuelle si non spécifiée
        if exam_date is None:
            exam_date = datetime.now()

        # Convertir les données en JSON
        probabilities_json = json.dumps(results['probabilities'])
        recommendations_json = json.dumps(results.get('recommendations', []))
        metadata_json = json.dumps(image_metadata) if image_metadata else None

        cursor.execute('''
            INSERT INTO examens_irm
            (patient_id, exam_date, filename, image_metadata, predicted_class, predicted_label,
             confidence, probabilities, description, recommendations, processing_time,
             user_session, ip_address)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            patient_id,
            exam_date,
            filename,
            metadata_json,
            results['predicted_class'],
            results['predicted_label'],
            results['confidence'],
            probabilities_json,
            results.get('description', ''),
            recommendations_json,
            processing_time,
            session_id,
            ip_address
        ))

        exam_id = cursor.lastrowid
        conn.commit()
        conn.close()
        return exam_id

    except Exception as e:
        print(f"Erreur lors de la sauvegarde de l'examen: {e}")
        return None

def get_patient_exam_history(patient_id, limit=None):
    """Récupérer l'historique des examens d'un patient"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        query = '''
            SELECT * FROM examens_irm
            WHERE patient_id = ?
            ORDER BY exam_date DESC
        '''

        if limit:
            query += f' LIMIT {limit}'

        cursor.execute(query, (patient_id,))
        exams = cursor.fetchall()

        conn.close()
        return exams

    except Exception as e:
        print(f"Erreur lors de la récupération de l'historique: {e}")
        return []

def analyze_tumor_evolution(patient_id, current_exam_id):
    """Analyser l'évolution tumorale en comparant avec les examens précédents"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        # Récupérer l'examen actuel
        cursor.execute('SELECT * FROM examens_irm WHERE id = ?', (current_exam_id,))
        current_exam = cursor.fetchone()

        if not current_exam:
            return None

        # Récupérer l'examen précédent le plus récent
        cursor.execute('''
            SELECT * FROM examens_irm
            WHERE patient_id = ? AND exam_date < ?
            ORDER BY exam_date DESC
            LIMIT 1
        ''', (patient_id, current_exam[2]))  # exam_date est à l'index 2

        previous_exam = cursor.fetchone()

        if not previous_exam:
            # Premier examen pour ce patient
            conn.close()
            return {
                'is_first_exam': True,
                'message': 'Premier examen pour ce patient - pas de comparaison possible'
            }

        # Calculer l'évolution
        evolution_data = calculate_evolution_metrics(previous_exam, current_exam)

        # Sauvegarder l'analyse d'évolution
        save_evolution_analysis(cursor, patient_id, previous_exam[0], current_exam_id, evolution_data)

        conn.commit()
        conn.close()

        return evolution_data

    except Exception as e:
        print(f"Erreur lors de l'analyse d'évolution: {e}")
        return None

def calculate_evolution_metrics(previous_exam, current_exam):
    """Calculer les métriques d'évolution entre deux examens"""
    try:
        # Parser les probabilités JSON
        prev_probs = json.loads(previous_exam[8])  # probabilities à l'index 8
        curr_probs = json.loads(current_exam[8])

        # Calculer l'intervalle de temps
        prev_date = datetime.fromisoformat(previous_exam[2])
        curr_date = datetime.fromisoformat(current_exam[2])
        time_interval = (curr_date - prev_date).days

        # Analyser les changements de probabilités
        prob_changes = {}
        max_change = 0
        max_change_type = None

        for tumor_type in prev_probs.keys():
            change = curr_probs[tumor_type] - prev_probs[tumor_type]
            prob_changes[tumor_type] = {
                'previous': prev_probs[tumor_type],
                'current': curr_probs[tumor_type],
                'change': change,
                'change_percent': (change / prev_probs[tumor_type] * 100) if prev_probs[tumor_type] > 0 else 0
            }

            if abs(change) > abs(max_change):
                max_change = change
                max_change_type = tumor_type

        # Déterminer le type d'évolution
        evolution_type = determine_evolution_type(previous_exam, current_exam, prob_changes)

        # Calculer le niveau d'alerte
        alert_level = calculate_alert_level(evolution_type, prob_changes, time_interval)

        # Générer l'interprétation médicale
        medical_interpretation = generate_medical_interpretation(
            previous_exam, current_exam, prob_changes, evolution_type, time_interval
        )

        return {
            'is_first_exam': False,
            'time_interval_days': time_interval,
            'evolution_type': evolution_type,
            'confidence_change': current_exam[6] - previous_exam[6],  # confidence à l'index 6
            'probability_changes': prob_changes,
            'alert_level': alert_level,
            'medical_interpretation': medical_interpretation,
            'previous_diagnosis': previous_exam[5],  # predicted_label à l'index 5
            'current_diagnosis': current_exam[5],
            'max_change_type': max_change_type,
            'max_change_value': max_change
        }

    except Exception as e:
        print(f"Erreur lors du calcul des métriques: {e}")
        return None

def determine_evolution_type(previous_exam, current_exam, prob_changes):
    """Déterminer le type d'évolution basé sur les changements"""
    prev_label = previous_exam[5]  # predicted_label
    curr_label = current_exam[5]

    # Changement de diagnostic principal
    if prev_label != curr_label:
        return 'changement_type'

    # Analyser les changements de probabilités pour le diagnostic actuel
    current_prob_change = prob_changes[curr_label]['change']

    if abs(current_prob_change) < 0.05:  # Changement < 5%
        return 'stable'
    elif current_prob_change > 0:
        return 'progression'
    else:
        return 'regression'

def calculate_alert_level(evolution_type, prob_changes, time_interval):
    """Calculer le niveau d'alerte basé sur l'évolution"""

    # Changement de type de tumeur
    if evolution_type == 'changement_type':
        # Vérifier si c'est un changement vers une tumeur maligne
        for tumor_type, change_data in prob_changes.items():
            if tumor_type in ['Gliome'] and change_data['change'] > 0.3:
                return 'critical'
        return 'high'

    # Progression rapide
    if evolution_type == 'progression':
        max_increase = max([change['change'] for change in prob_changes.values() if change['change'] > 0], default=0)

        if time_interval < 90 and max_increase > 0.4:  # >40% en moins de 3 mois
            return 'critical'
        elif time_interval < 180 and max_increase > 0.3:  # >30% en moins de 6 mois
            return 'high'
        elif max_increase > 0.2:  # >20%
            return 'medium'
        else:
            return 'low'

    # Régression (amélioration)
    if evolution_type == 'regression':
        return 'low'

    # Stable
    return 'none'

def generate_medical_interpretation(previous_exam, current_exam, prob_changes, evolution_type, time_interval):
    """Générer une interprétation médicale de l'évolution"""

    prev_label = previous_exam[5]
    curr_label = current_exam[5]
    prev_confidence = previous_exam[6]
    curr_confidence = current_exam[6]

    interpretation = []

    # Analyse du diagnostic principal
    if prev_label != curr_label:
        interpretation.append(f"Changement de diagnostic : {prev_label} → {curr_label}")
        interpretation.append(f"Confiance : {prev_confidence:.1%} → {curr_confidence:.1%}")
    else:
        interpretation.append(f"Diagnostic stable : {curr_label}")
        confidence_change = curr_confidence - prev_confidence
        if abs(confidence_change) > 0.1:
            direction = "augmentation" if confidence_change > 0 else "diminution"
            interpretation.append(f"Confiance du diagnostic : {direction} de {abs(confidence_change):.1%}")

    # Analyse temporelle
    if time_interval < 30:
        interpretation.append(f"Suivi rapproché ({time_interval} jours)")
    elif time_interval < 90:
        interpretation.append(f"Suivi à court terme ({time_interval} jours)")
    elif time_interval < 365:
        interpretation.append(f"Suivi à moyen terme ({time_interval} jours)")
    else:
        interpretation.append(f"Suivi à long terme ({time_interval} jours)")

    # Analyse des changements significatifs
    significant_changes = []
    for tumor_type, change_data in prob_changes.items():
        if abs(change_data['change']) > 0.1:  # Changement > 10%
            direction = "↗" if change_data['change'] > 0 else "↘"
            significant_changes.append(f"{tumor_type}: {direction} {abs(change_data['change']):.1%}")

    if significant_changes:
        interpretation.append("Changements significatifs : " + ", ".join(significant_changes))

    # Recommandations basées sur l'évolution
    if evolution_type == 'changement_type':
        interpretation.append("⚠️ Recommandation : Réévaluation clinique urgente recommandée")
    elif evolution_type == 'progression':
        interpretation.append("📈 Recommandation : Surveillance renforcée et consultation spécialisée")
    elif evolution_type == 'regression':
        interpretation.append("📉 Évolution favorable - Maintenir le suivi habituel")
    else:
        interpretation.append("📊 Évolution stable - Suivi de routine")

    return " | ".join(interpretation)

def save_evolution_analysis(cursor, patient_id, prev_exam_id, curr_exam_id, evolution_data):
    """Sauvegarder l'analyse d'évolution dans la base de données"""
    try:
        cursor.execute('''
            INSERT INTO evolution_analysis
            (patient_id, exam_id_previous, exam_id_current, time_interval_days,
             evolution_type, confidence_change, probability_changes, alert_level,
             medical_interpretation, recommendations)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            patient_id,
            prev_exam_id,
            curr_exam_id,
            evolution_data['time_interval_days'],
            evolution_data['evolution_type'],
            evolution_data['confidence_change'],
            json.dumps(evolution_data['probability_changes']),
            evolution_data['alert_level'],
            evolution_data['medical_interpretation'],
            json.dumps([])  # Recommandations à implémenter
        ))

        return True

    except Exception as e:
        print(f"Erreur lors de la sauvegarde de l'analyse d'évolution: {e}")
        return False

def get_patient_evolution_summary(patient_id):
    """Obtenir un résumé de l'évolution d'un patient"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        # Récupérer les informations du patient
        cursor.execute('SELECT * FROM patients WHERE patient_id = ?', (patient_id,))
        patient = cursor.fetchone()

        if not patient:
            return None

        # Récupérer tous les examens
        cursor.execute('''
            SELECT * FROM examens_irm
            WHERE patient_id = ?
            ORDER BY exam_date ASC
        ''', (patient_id,))
        exams = cursor.fetchall()

        # Récupérer les analyses d'évolution
        cursor.execute('''
            SELECT * FROM evolution_analysis
            WHERE patient_id = ?
            ORDER BY created_at ASC
        ''', (patient_id,))
        evolutions = cursor.fetchall()

        conn.close()

        return {
            'patient': patient,
            'exams': exams,
            'evolutions': evolutions,
            'total_exams': len(exams),
            'evolution_analyses': len(evolutions)
        }

    except Exception as e:
        print(f"Erreur lors de la récupération du résumé d'évolution: {e}")
        return None

# Initialiser la base de données au démarrage
init_database()

# Définition du modèle CNN (architecture exacte du modèle sauvegardé)
class BrainTumorCNN(nn.Module):
    def __init__(self, num_classes=4):  # 4 classes: Normal, Glioma, Meningioma, Pituitary
        super(BrainTumorCNN, self).__init__()

        # Couches de convolution (architecture exacte du modèle sauvegardé)
        self.conv1 = nn.Conv2d(3, 32, kernel_size=3, padding=1)
        self.conv2 = nn.Conv2d(32, 64, kernel_size=3, padding=1)
        self.conv3 = nn.Conv2d(64, 128, kernel_size=3, padding=1)
        self.conv4 = nn.Conv2d(128, 256, kernel_size=3, padding=1)
        self.conv5 = nn.Conv2d(256, 256, kernel_size=3, padding=1)

        # Couches de pooling et autres
        self.pool = nn.MaxPool2d(2, 2)
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.5)
        self.flatten = nn.Flatten()

        # Couches fully connected (tailles exactes du modèle sauvegardé)
        self.fc1 = nn.Linear(12544, 512)  # Taille exacte détectée: 256*7*7 = 12544
        self.fc2 = nn.Linear(512, num_classes)

    def forward(self, x):
        # Appliquer les couches de convolution avec pooling
        x = self.pool(self.relu(self.conv1(x)))
        x = self.pool(self.relu(self.conv2(x)))
        x = self.pool(self.relu(self.conv3(x)))
        x = self.pool(self.relu(self.conv4(x)))
        x = self.pool(self.relu(self.conv5(x)))

        # Aplatir et appliquer les couches FC
        x = self.flatten(x)
        x = self.dropout(self.relu(self.fc1(x)))
        x = self.fc2(x)

        return x

# Charger le modèle
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
model = BrainTumorCNN(num_classes=4)

try:
    # Charger les poids du modèle
    checkpoint = torch.load('best_brain_tumor_model.pth', map_location=device)
    model.load_state_dict(checkpoint)
    model.to(device)
    model.eval()
    print("Modèle chargé avec succès!")
except Exception as e:
    print(f"Erreur lors du chargement du modèle: {e}")
    model = None

# Classes de tumeurs
TUMOR_CLASSES = {
    0: 'Normal',
    1: 'Gliome',
    2: 'Méningiome', 
    3: 'Tumeur pituitaire'
}

# Transformations pour les images
transform = transforms.Compose([
    transforms.Resize((224, 224)),
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
])

def allowed_file(filename):
    """Vérifier si le fichier est autorisé"""
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'dcm', 'nii'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def preprocess_image(image_path):
    """Préprocesser l'image pour le modèle"""
    try:
        # Charger l'image
        image = Image.open(image_path)
        
        # Convertir en RGB si nécessaire
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # Appliquer les transformations
        image_tensor = transform(image).unsqueeze(0)  # Ajouter dimension batch
        
        return image_tensor
    except Exception as e:
        print(f"Erreur lors du préprocessing: {e}")
        return None

def predict_tumor(image_path):
    """Prédire le type de tumeur"""
    if model is None:
        return None
    
    try:
        # Préprocesser l'image
        image_tensor = preprocess_image(image_path)
        if image_tensor is None:
            return None
        
        # Faire la prédiction
        with torch.no_grad():
            image_tensor = image_tensor.to(device)
            outputs = model(image_tensor)
            probabilities = F.softmax(outputs, dim=1)
            
            # Obtenir les probabilités pour chaque classe
            probs = probabilities.cpu().numpy()[0]
            predicted_class = np.argmax(probs)
            
            results = {
                'predicted_class': int(predicted_class),
                'predicted_label': TUMOR_CLASSES[predicted_class],
                'confidence': float(probs[predicted_class]),
                'probabilities': {
                    'Normal': float(probs[0]),
                    'Gliome': float(probs[1]),
                    'Méningiome': float(probs[2]),
                    'Tumeur pituitaire': float(probs[3])
                }
            }

            # Ajouter la description et les recommandations Gemini
            gemini_analysis = get_gemini_analysis(results)
            if gemini_analysis:
                results['description'] = gemini_analysis.get('description', '')
                results['recommendations'] = gemini_analysis.get('recommendations', get_recommendations(results))
            else:
                results['recommendations'] = get_recommendations(results)

            return results
            
    except Exception as e:
        print(f"Erreur lors de la prédiction: {e}")
        return None

@app.route('/')
def index():
    """Page d'accueil"""
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    """Gérer l'upload et l'analyse d'image"""
    start_time = time.time()

    if 'file' not in request.files:
        return jsonify({'error': 'Aucun fichier fourni'}), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'Aucun fichier sélectionné'}), 400

    if file and allowed_file(file.filename):
        try:
            # Sauvegarder le fichier
            filename = secure_filename(file.filename)
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)

            # Faire la prédiction
            results = predict_tumor(filepath)

            if results is None:
                return jsonify({'error': 'Erreur lors de l\'analyse de l\'image'}), 500

            # Calculer le temps de traitement
            processing_time = time.time() - start_time

            # Convertir l'image en base64 pour l'affichage
            with open(filepath, 'rb') as img_file:
                img_data = base64.b64encode(img_file.read()).decode()
                img_url = f"data:image/jpeg;base64,{img_data}"

            # Nettoyer le fichier temporaire
            os.remove(filepath)

            # Sauvegarder l'analyse dans la base de données
            session_id = request.headers.get('X-Session-ID', 'anonymous')
            ip_address = request.remote_addr
            save_analysis_to_db(results, filename, processing_time, session_id, ip_address)

            # Préparer la réponse
            response = {
                'success': True,
                'image_url': img_url,
                'prediction': results['predicted_label'],
                'confidence': results['confidence'],
                'probabilities': results['probabilities'],
                'is_tumor': results['predicted_class'] != 0,  # 0 = Normal
                'recommendations': results.get('recommendations', get_recommendations(results)),
                'description': results.get('description', ''),
                'processing_time': round(processing_time, 2)
            }

            return jsonify(response)

        except Exception as e:
            print(f"Erreur lors du traitement: {e}")
            return jsonify({'error': 'Erreur lors du traitement de l\'image'}), 500

    return jsonify({'error': 'Type de fichier non autorisé'}), 400

def call_gemini_api(prompt, context="medical"):
    """Appeler l'API Gemini avec un prompt"""
    try:
        headers = {
            'Content-Type': 'application/json',
        }

        # Prompt système pour limiter aux domaines médicaux
        system_prompt = """Tu es un assistant médical spécialisé en neurologie et en imagerie médicale.
        Tu dois UNIQUEMENT répondre aux questions liées au domaine médical, particulièrement :
        - Neurologie et neurochirurgie
        - Imagerie médicale (IRM, scanner, etc.)
        - Tumeurs cérébrales et pathologies neurologiques
        - Diagnostic et recommandations cliniques

        Si une question n'est pas liée au domaine médical, réponds poliment que tu ne peux traiter que les questions médicales.
        Tes réponses doivent être précises, professionnelles et basées sur les connaissances médicales actuelles.
        Ajoute toujours un disclaimer rappelant que tes conseils ne remplacent pas une consultation médicale."""

        data = {
            "contents": [
                {
                    "parts": [
                        {
                            "text": f"{system_prompt}\n\nQuestion: {prompt}"
                        }
                    ]
                }
            ],
            "generationConfig": {
                "temperature": 0.7,
                "topK": 40,
                "topP": 0.95,
                "maxOutputTokens": 1024,
            }
        }

        response = requests.post(
            f"{GEMINI_API_URL}?key={GEMINI_API_KEY}",
            headers=headers,
            json=data,
            timeout=30
        )

        if response.status_code == 200:
            result = response.json()
            if 'candidates' in result and len(result['candidates']) > 0:
                return result['candidates'][0]['content']['parts'][0]['text']

        print(f"Erreur API Gemini: {response.status_code} - {response.text}")
        return None

    except Exception as e:
        print(f"Erreur lors de l'appel à Gemini: {e}")
        return None

def get_gemini_analysis(results):
    """Obtenir une analyse détaillée de Gemini pour les résultats"""
    try:
        prompt = f"""
        Analyse les résultats suivants d'une IRM cérébrale analysée par IA :

        - Diagnostic principal: {results['predicted_label']}
        - Niveau de confiance: {results['confidence']*100:.1f}%
        - Probabilités:
          * Normal: {results['probabilities']['Normal']*100:.1f}%
          * Gliome: {results['probabilities']['Gliome']*100:.1f}%
          * Méningiome: {results['probabilities']['Méningiome']*100:.1f}%
          * Tumeur pituitaire: {results['probabilities']['Tumeur pituitaire']*100:.1f}%

        Fournis une réponse structurée avec :
        1. DESCRIPTION: Une explication claire et détaillée du diagnostic (2-3 phrases)
        2. RECOMMANDATIONS: 3-4 recommandations cliniques spécifiques et pratiques

        Format ta réponse exactement comme ceci :
        DESCRIPTION: [ton explication]
        RECOMMANDATIONS:
        - [recommandation 1]
        - [recommandation 2]
        - [recommandation 3]
        """

        response = call_gemini_api(prompt)
        if response:
            # Parser la réponse
            lines = response.strip().split('\n')
            description = ""
            recommendations = []

            current_section = None
            for line in lines:
                line = line.strip()
                if line.startswith('DESCRIPTION:'):
                    description = line.replace('DESCRIPTION:', '').strip()
                    current_section = 'description'
                elif line.startswith('RECOMMANDATIONS:'):
                    current_section = 'recommendations'
                elif line.startswith('-') and current_section == 'recommendations':
                    recommendations.append(line[1:].strip())
                elif current_section == 'description' and line:
                    description += " " + line

            return {
                'description': description,
                'recommendations': recommendations if recommendations else get_recommendations(results)
            }

    except Exception as e:
        print(f"Erreur lors de l'analyse Gemini: {e}")

    return None

def get_recommendations(results):
    """Générer des recommandations basées sur les résultats (fallback)"""
    recommendations = []

    if results['predicted_class'] == 0:  # Normal
        recommendations = [
            "Aucune anomalie détectée dans cette analyse",
            "Suivi de routine recommandé selon les protocoles standards",
            "Consultation avec un radiologue pour confirmation"
        ]
    else:  # Tumeur détectée
        recommendations = [
            "Biopsie recommandée pour confirmation histologique",
            "IRM de suivi dans 3 mois pour évaluation de la croissance",
            "Consultation avec un neuro-oncologue spécialisé"
        ]

        if results['confidence'] < 0.7:
            recommendations.append("Analyse complémentaire recommandée en raison de la faible confiance")

    return recommendations

@app.route('/health')
def health_check():
    """Vérification de l'état de l'application"""
    return jsonify({
        'status': 'healthy',
        'model_loaded': model is not None,
        'device': str(device)
    })

@app.route('/generate-report', methods=['POST'])
def generate_report():
    """Générer un rapport médical"""
    try:
        data = request.get_json()

        # Valider les données requises
        required_fields = ['patientName', 'analysisData']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'Champ requis manquant: {field}'}), 400

        # Générer le rapport
        report_content = create_medical_report(data)

        # Simuler la sauvegarde du rapport
        report_id = f"RPT_{int(time.time())}"

        return jsonify({
            'success': True,
            'report_id': report_id,
            'message': 'Rapport généré avec succès',
            'download_url': f'/download-report/{report_id}'
        })

    except Exception as e:
        print(f"Erreur lors de la génération du rapport: {e}")
        return jsonify({'error': 'Erreur lors de la génération du rapport'}), 500

@app.route('/share-analysis', methods=['POST'])
def share_analysis():
    """Partager une analyse avec un collègue"""
    try:
        data = request.get_json()

        # Valider les données requises
        required_fields = ['recipientEmail', 'analysisData']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'Champ requis manquant: {field}'}), 400

        # Simuler l'envoi de l'email
        share_id = f"SHR_{int(time.time())}"

        # Dans une vraie application, ici on enverrait un email
        send_analysis_email(data)

        return jsonify({
            'success': True,
            'share_id': share_id,
            'message': f'Analyse partagée avec {data["recipientEmail"]}'
        })

    except Exception as e:
        print(f"Erreur lors du partage: {e}")
        return jsonify({'error': 'Erreur lors du partage'}), 500

@app.route('/chat', methods=['POST'])
def chat_with_bot():
    """Chatbot médical avec Gemini"""
    try:
        data = request.get_json()
        message = data.get('message', '').strip()

        if not message:
            return jsonify({'error': 'Message vide'}), 400

        # Appeler Gemini pour la réponse
        response = call_gemini_api(message, context="medical_consultation")

        if response:
            return jsonify({
                'success': True,
                'response': response
            })
        else:
            return jsonify({
                'success': False,
                'response': 'Désolé, je ne peux pas répondre pour le moment. Veuillez réessayer.'
            })

    except Exception as e:
        print(f"Erreur chatbot: {e}")
        return jsonify({
            'success': False,
            'response': 'Une erreur s\'est produite. Veuillez réessayer.'
        }), 500

@app.route('/pro-dashboard')
def pro_dashboard():
    """Page du tableau de bord professionnel"""
    return render_template('pro_dashboard.html')

@app.route('/pro-dashboard-advanced')
def pro_dashboard_advanced():
    """Page du tableau de bord professionnel avancé"""
    return render_template('pro_dashboard_advanced.html')

@app.route('/api/analytics/overview')
def analytics_overview():
    """API pour les statistiques générales"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        # Statistiques générales
        cursor.execute('SELECT COUNT(*) FROM analyses')
        total_analyses = cursor.fetchone()[0]

        cursor.execute('SELECT COUNT(DISTINCT DATE(timestamp)) FROM analyses')
        active_days = cursor.fetchone()[0]

        cursor.execute('SELECT AVG(confidence) FROM analyses')
        avg_confidence = cursor.fetchone()[0] or 0

        cursor.execute('SELECT AVG(processing_time) FROM analyses')
        avg_processing_time = cursor.fetchone()[0] or 0

        # Répartition par type de tumeur
        cursor.execute('''
            SELECT predicted_label, COUNT(*)
            FROM analyses
            GROUP BY predicted_label
        ''')
        tumor_distribution = dict(cursor.fetchall())

        # Analyses par jour (30 derniers jours)
        cursor.execute('''
            SELECT DATE(timestamp) as date, COUNT(*) as count
            FROM analyses
            WHERE timestamp >= date('now', '-30 days')
            GROUP BY DATE(timestamp)
            ORDER BY date
        ''')
        daily_analyses = cursor.fetchall()

        conn.close()

        return jsonify({
            'success': True,
            'data': {
                'total_analyses': total_analyses,
                'active_days': active_days,
                'avg_confidence': round(avg_confidence * 100, 1) if avg_confidence else 0,
                'avg_processing_time': round(avg_processing_time, 2) if avg_processing_time else 0,
                'tumor_distribution': tumor_distribution,
                'daily_analyses': daily_analyses
            }
        })

    except Exception as e:
        print(f"Erreur analytics overview: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/analytics/period/<period>')
def analytics_by_period(period):
    """API pour les statistiques par période (day/month/year)"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        if period == 'day':
            # Analyses par heure pour le jour le plus récent avec des données
            cursor.execute('''
                SELECT strftime('%H', timestamp) as hour, COUNT(*) as count
                FROM analyses
                WHERE DATE(timestamp) = (
                    SELECT DATE(timestamp) FROM analyses
                    ORDER BY timestamp DESC LIMIT 1
                )
                GROUP BY strftime('%H', timestamp)
                ORDER BY hour
            ''')
            data = cursor.fetchall()

            # Si pas de données pour le jour le plus récent, prendre les 24 dernières heures
            if not data:
                cursor.execute('''
                    SELECT strftime('%H', timestamp) as hour, COUNT(*) as count
                    FROM analyses
                    WHERE timestamp >= datetime('now', '-24 hours')
                    GROUP BY strftime('%H', timestamp)
                    ORDER BY hour
                ''')
                data = cursor.fetchall()

            # Créer un tableau complet de 24 heures
            hour_counts = {str(hour).zfill(2): 0 for hour in range(24)}
            for hour, count in data:
                hour_counts[hour] = count

            labels = [f"{hour}h" for hour in sorted(hour_counts.keys())]
            values = [hour_counts[hour] for hour in sorted(hour_counts.keys())]

        elif period == 'month':
            # Analyses par jour pour le mois le plus récent avec des données
            cursor.execute('''
                SELECT strftime('%d', timestamp) as day, COUNT(*) as count
                FROM analyses
                WHERE strftime('%Y-%m', timestamp) = (
                    SELECT strftime('%Y-%m', timestamp) FROM analyses
                    ORDER BY timestamp DESC LIMIT 1
                )
                GROUP BY strftime('%d', timestamp)
                ORDER BY CAST(day as INTEGER)
            ''')
            data = cursor.fetchall()

            # Si pas de données, prendre les 30 derniers jours
            if not data:
                cursor.execute('''
                    SELECT strftime('%d', timestamp) as day, COUNT(*) as count
                    FROM analyses
                    WHERE timestamp >= date('now', '-30 days')
                    GROUP BY strftime('%d', timestamp)
                    ORDER BY CAST(day as INTEGER)
                ''')
                data = cursor.fetchall()

            labels = [f"{day}" for day, _ in data]
            values = [count for _, count in data]

        elif period == 'year':
            # Analyses par mois pour l'année la plus récente avec des données
            cursor.execute('''
                SELECT strftime('%m', timestamp) as month, COUNT(*) as count
                FROM analyses
                WHERE strftime('%Y', timestamp) = (
                    SELECT strftime('%Y', timestamp) FROM analyses
                    ORDER BY timestamp DESC LIMIT 1
                )
                GROUP BY strftime('%m', timestamp)
                ORDER BY CAST(month as INTEGER)
            ''')
            data = cursor.fetchall()

            # Si pas de données, prendre les 12 derniers mois
            if not data:
                cursor.execute('''
                    SELECT strftime('%m', timestamp) as month, COUNT(*) as count
                    FROM analyses
                    WHERE timestamp >= date('now', '-12 months')
                    GROUP BY strftime('%m', timestamp)
                    ORDER BY CAST(month as INTEGER)
                ''')
                data = cursor.fetchall()

            month_names = ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun',
                          'Jul', 'Aoû', 'Sep', 'Oct', 'Nov', 'Déc']
            labels = [month_names[int(month)-1] for month, _ in data]
            values = [count for _, count in data]

        else:
            return jsonify({'success': False, 'error': 'Période invalide'}), 400

        conn.close()

        return jsonify({
            'success': True,
            'data': {
                'labels': labels,
                'values': values,
                'period': period
            }
        })

    except Exception as e:
        print(f"Erreur analytics period: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/analytics/recent')
def recent_analyses():
    """API pour les analyses récentes"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        cursor.execute('''
            SELECT timestamp, filename, predicted_label, confidence, processing_time
            FROM analyses
            ORDER BY timestamp DESC
            LIMIT 10
        ''')

        analyses = []
        for row in cursor.fetchall():
            analyses.append({
                'timestamp': row[0],
                'filename': row[1],
                'predicted_label': row[2],
                'confidence': round(row[3] * 100, 1),
                'processing_time': round(row[4], 2)
            })

        conn.close()

        return jsonify({
            'success': True,
            'data': analyses
        })

    except Exception as e:
        print(f"Erreur recent analyses: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/analytics/export/<format>')
def export_analytics(format):
    """API pour exporter les données analytiques"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        if format == 'csv':
            cursor.execute('''
                SELECT timestamp, filename, predicted_label, confidence,
                       processing_time, description
                FROM analyses
                ORDER BY timestamp DESC
            ''')

            data = cursor.fetchall()

            # Créer le contenu CSV
            csv_content = "Timestamp,Filename,Diagnostic,Confidence,Processing_Time,Description\n"
            for row in data:
                csv_content += f'"{row[0]}","{row[1]}","{row[2]}",{row[3]:.3f},{row[4]:.2f},"{row[5] or ""}"\n'

            conn.close()

            return Response(
                csv_content,
                mimetype='text/csv',
                headers={'Content-Disposition': f'attachment; filename=neuroscan_analytics_{datetime.now().strftime("%Y%m%d")}.csv'}
            )

        elif format == 'json':
            cursor.execute('''
                SELECT timestamp, filename, predicted_label, confidence,
                       processing_time, probabilities, description, recommendations
                FROM analyses
                ORDER BY timestamp DESC
            ''')

            analyses = []
            for row in cursor.fetchall():
                analyses.append({
                    'timestamp': row[0],
                    'filename': row[1],
                    'predicted_label': row[2],
                    'confidence': row[3],
                    'processing_time': row[4],
                    'probabilities': json.loads(row[5]) if row[5] else {},
                    'description': row[6],
                    'recommendations': json.loads(row[7]) if row[7] else []
                })

            conn.close()

            export_data = {
                'export_date': datetime.now().isoformat(),
                'total_analyses': len(analyses),
                'analyses': analyses
            }

            return Response(
                json.dumps(export_data, indent=2),
                mimetype='application/json',
                headers={'Content-Disposition': f'attachment; filename=neuroscan_analytics_{datetime.now().strftime("%Y%m%d")}.json'}
            )

        else:
            return jsonify({'success': False, 'error': 'Format non supporté'}), 400

    except Exception as e:
        print(f"Erreur export analytics: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/analytics/stats/advanced')
def advanced_stats():
    """API pour des statistiques avancées"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        # Statistiques par heure de la journée
        cursor.execute('''
            SELECT strftime('%H', timestamp) as hour, COUNT(*) as count
            FROM analyses
            GROUP BY strftime('%H', timestamp)
            ORDER BY hour
        ''')
        hourly_stats = dict(cursor.fetchall())

        # Évolution de la confiance dans le temps
        cursor.execute('''
            SELECT DATE(timestamp) as date, AVG(confidence) as avg_confidence
            FROM analyses
            WHERE timestamp >= date('now', '-30 days')
            GROUP BY DATE(timestamp)
            ORDER BY date
        ''')
        confidence_evolution = cursor.fetchall()

        # Top 5 des jours les plus actifs
        cursor.execute('''
            SELECT DATE(timestamp) as date, COUNT(*) as count
            FROM analyses
            GROUP BY DATE(timestamp)
            ORDER BY count DESC
            LIMIT 5
        ''')
        top_active_days = cursor.fetchall()

        # Statistiques de performance
        cursor.execute('''
            SELECT
                MIN(processing_time) as min_time,
                MAX(processing_time) as max_time,
                AVG(processing_time) as avg_time,
                COUNT(CASE WHEN processing_time < 5 THEN 1 END) as fast_analyses,
                COUNT(CASE WHEN processing_time >= 5 THEN 1 END) as slow_analyses
            FROM analyses
        ''')
        performance_stats = cursor.fetchone()

        conn.close()

        return jsonify({
            'success': True,
            'data': {
                'hourly_distribution': hourly_stats,
                'confidence_evolution': confidence_evolution,
                'top_active_days': top_active_days,
                'performance_stats': {
                    'min_processing_time': performance_stats[0],
                    'max_processing_time': performance_stats[1],
                    'avg_processing_time': performance_stats[2],
                    'fast_analyses': performance_stats[3],
                    'slow_analyses': performance_stats[4]
                }
            }
        })

    except Exception as e:
        print(f"Erreur advanced stats: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/analytics/filters')
def get_filter_options():
    """API pour obtenir les options de filtres disponibles"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        # Obtenir les plages de dates disponibles
        cursor.execute('''
            SELECT MIN(DATE(timestamp)) as min_date, MAX(DATE(timestamp)) as max_date
            FROM analyses
        ''')
        date_range = cursor.fetchone()

        # Obtenir les types de diagnostics
        cursor.execute('SELECT DISTINCT predicted_label FROM analyses ORDER BY predicted_label')
        diagnostic_types = [row[0] for row in cursor.fetchall()]

        # Obtenir les plages de confiance
        cursor.execute('SELECT MIN(confidence), MAX(confidence) FROM analyses')
        confidence_range = cursor.fetchone()

        conn.close()

        return jsonify({
            'success': True,
            'data': {
                'date_range': {
                    'min': date_range[0],
                    'max': date_range[1]
                },
                'diagnostic_types': diagnostic_types,
                'confidence_range': {
                    'min': round(confidence_range[0] * 100, 1) if confidence_range[0] else 0,
                    'max': round(confidence_range[1] * 100, 1) if confidence_range[1] else 100
                }
            }
        })

    except Exception as e:
        print(f"Erreur filter options: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/analytics/filtered', methods=['POST'])
def get_filtered_analytics():
    """API pour obtenir des analyses filtrées"""
    try:
        filters = request.get_json()
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        # Construire la requête avec filtres
        where_conditions = []
        params = []

        if filters.get('start_date'):
            where_conditions.append('DATE(timestamp) >= ?')
            params.append(filters['start_date'])

        if filters.get('end_date'):
            where_conditions.append('DATE(timestamp) <= ?')
            params.append(filters['end_date'])

        if filters.get('diagnostic_types'):
            placeholders = ','.join(['?' for _ in filters['diagnostic_types']])
            where_conditions.append(f'predicted_label IN ({placeholders})')
            params.extend(filters['diagnostic_types'])

        if filters.get('min_confidence'):
            where_conditions.append('confidence >= ?')
            params.append(filters['min_confidence'] / 100)

        if filters.get('max_confidence'):
            where_conditions.append('confidence <= ?')
            params.append(filters['max_confidence'] / 100)

        where_clause = 'WHERE ' + ' AND '.join(where_conditions) if where_conditions else ''

        # Obtenir les analyses filtrées
        cursor.execute(f'''
            SELECT timestamp, filename, predicted_label, confidence, processing_time, description
            FROM analyses
            {where_clause}
            ORDER BY timestamp DESC
            LIMIT 100
        ''', params)

        analyses = []
        for row in cursor.fetchall():
            analyses.append({
                'timestamp': row[0],
                'filename': row[1],
                'predicted_label': row[2],
                'confidence': round(row[3] * 100, 1),
                'processing_time': round(row[4], 2),
                'description': row[5] or ''
            })

        # Statistiques des résultats filtrés
        cursor.execute(f'''
            SELECT
                COUNT(*) as total,
                AVG(confidence) as avg_confidence,
                AVG(processing_time) as avg_time,
                predicted_label,
                COUNT(*) as type_count
            FROM analyses
            {where_clause}
            GROUP BY predicted_label
        ''', params)

        stats_by_type = {}
        total_filtered = 0
        total_confidence = 0
        total_time = 0

        for row in cursor.fetchall():
            stats_by_type[row[3]] = row[4]
            total_filtered += row[4]
            total_confidence += row[1] * row[4] if row[1] else 0
            total_time += row[2] * row[4] if row[2] else 0

        conn.close()

        return jsonify({
            'success': True,
            'data': {
                'analyses': analyses,
                'stats': {
                    'total': total_filtered,
                    'avg_confidence': round((total_confidence / total_filtered * 100), 1) if total_filtered > 0 else 0,
                    'avg_processing_time': round((total_time / total_filtered), 2) if total_filtered > 0 else 0,
                    'distribution': stats_by_type
                }
            }
        })

    except Exception as e:
        print(f"Erreur filtered analytics: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/analytics/comparison')
def get_comparison_data():
    """API pour les données de comparaison temporelle"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        # Comparaison cette semaine vs semaine dernière
        cursor.execute('''
            SELECT
                CASE
                    WHEN DATE(timestamp) >= DATE('now', '-7 days') THEN 'Cette semaine'
                    WHEN DATE(timestamp) >= DATE('now', '-14 days') THEN 'Semaine dernière'
                END as period,
                COUNT(*) as count,
                AVG(confidence) as avg_confidence,
                predicted_label
            FROM analyses
            WHERE DATE(timestamp) >= DATE('now', '-14 days')
            GROUP BY period, predicted_label
            ORDER BY period, predicted_label
        ''')

        weekly_comparison = {}
        for row in cursor.fetchall():
            period = row[0]
            if period not in weekly_comparison:
                weekly_comparison[period] = {}
            weekly_comparison[period][row[3]] = {
                'count': row[1],
                'avg_confidence': round(row[2] * 100, 1) if row[2] else 0
            }

        # Comparaison ce mois vs mois dernier
        cursor.execute('''
            SELECT
                CASE
                    WHEN strftime('%Y-%m', timestamp) = strftime('%Y-%m', 'now') THEN 'Ce mois'
                    WHEN strftime('%Y-%m', timestamp) = strftime('%Y-%m', 'now', '-1 month') THEN 'Mois dernier'
                END as period,
                COUNT(*) as count,
                AVG(confidence) as avg_confidence
            FROM analyses
            WHERE strftime('%Y-%m', timestamp) >= strftime('%Y-%m', 'now', '-1 month')
            GROUP BY period
        ''')

        monthly_comparison = {}
        for row in cursor.fetchall():
            if row[0]:  # Vérifier que period n'est pas None
                monthly_comparison[row[0]] = {
                    'count': row[1],
                    'avg_confidence': round(row[2] * 100, 1) if row[2] else 0
                }

        conn.close()

        return jsonify({
            'success': True,
            'data': {
                'weekly': weekly_comparison,
                'monthly': monthly_comparison
            }
        })

    except Exception as e:
        print(f"Erreur comparison data: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/analytics/alerts')
def get_alerts():
    """API pour obtenir les alertes et notifications"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        alerts = []

        # Alerte: Baisse de confiance moyenne
        cursor.execute('''
            SELECT AVG(confidence) as avg_conf_today
            FROM analyses
            WHERE DATE(timestamp) = DATE('now')
        ''')
        today_confidence = cursor.fetchone()[0]

        cursor.execute('''
            SELECT AVG(confidence) as avg_conf_week
            FROM analyses
            WHERE DATE(timestamp) >= DATE('now', '-7 days')
            AND DATE(timestamp) < DATE('now')
        ''')
        week_confidence = cursor.fetchone()[0]

        if today_confidence and week_confidence and today_confidence < week_confidence * 0.9:
            alerts.append({
                'type': 'warning',
                'title': 'Baisse de confiance détectée',
                'message': f'La confiance moyenne aujourd\'hui ({today_confidence*100:.1f}%) est inférieure à la moyenne de la semaine ({week_confidence*100:.1f}%)',
                'timestamp': datetime.now().isoformat()
            })

        # Alerte: Pic d'activité
        cursor.execute('''
            SELECT COUNT(*) as today_count
            FROM analyses
            WHERE DATE(timestamp) = DATE('now')
        ''')
        today_count = cursor.fetchone()[0]

        cursor.execute('''
            SELECT AVG(daily_count) as avg_daily
            FROM (
                SELECT COUNT(*) as daily_count
                FROM analyses
                WHERE DATE(timestamp) >= DATE('now', '-7 days')
                AND DATE(timestamp) < DATE('now')
                GROUP BY DATE(timestamp)
            )
        ''')
        avg_daily = cursor.fetchone()[0]

        if today_count and avg_daily and today_count > avg_daily * 1.5:
            alerts.append({
                'type': 'info',
                'title': 'Pic d\'activité détecté',
                'message': f'Nombre d\'analyses aujourd\'hui ({today_count}) supérieur à la moyenne ({avg_daily:.1f})',
                'timestamp': datetime.now().isoformat()
            })

        # Alerte: Analyses avec faible confiance
        cursor.execute('''
            SELECT COUNT(*) as low_conf_count
            FROM analyses
            WHERE DATE(timestamp) = DATE('now')
            AND confidence < 0.7
        ''')
        low_confidence_count = cursor.fetchone()[0]

        if low_confidence_count > 0:
            alerts.append({
                'type': 'warning',
                'title': 'Analyses à faible confiance',
                'message': f'{low_confidence_count} analyse(s) avec confiance < 70% aujourd\'hui',
                'timestamp': datetime.now().isoformat()
            })

        conn.close()

        return jsonify({
            'success': True,
            'data': alerts
        })

    except Exception as e:
        print(f"Erreur alerts: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/analytics/performance')
def get_performance_trends():
    """API pour les tendances de performance"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        # Tendances de confiance sur les 7 derniers jours
        cursor.execute('''
            SELECT
                DATE(timestamp) as date,
                AVG(confidence) as avg_confidence,
                AVG(processing_time) as avg_time,
                COUNT(*) as daily_count
            FROM analyses
            WHERE DATE(timestamp) >= DATE('now', '-7 days')
            GROUP BY DATE(timestamp)
            ORDER BY date
        ''')

        daily_trends = cursor.fetchall()

        # Tendances par heure pour aujourd'hui
        cursor.execute('''
            SELECT
                strftime('%H', timestamp) as hour,
                AVG(confidence) as avg_confidence,
                AVG(processing_time) as avg_time,
                COUNT(*) as hourly_count
            FROM analyses
            WHERE DATE(timestamp) = DATE('now')
            GROUP BY strftime('%H', timestamp)
            ORDER BY hour
        ''')

        hourly_trends = cursor.fetchall()

        # Performance par type de diagnostic
        cursor.execute('''
            SELECT
                predicted_label,
                AVG(confidence) as avg_confidence,
                AVG(processing_time) as avg_time,
                COUNT(*) as count
            FROM analyses
            WHERE DATE(timestamp) >= DATE('now', '-7 days')
            GROUP BY predicted_label
            ORDER BY predicted_label
        ''')

        performance_by_type = cursor.fetchall()

        conn.close()

        # Formater les données pour Chart.js
        daily_data = {
            'labels': [row[0] for row in daily_trends],
            'confidence': [round(row[1] * 100, 1) if row[1] else 0 for row in daily_trends],
            'processing_time': [round(row[2], 2) if row[2] else 0 for row in daily_trends],
            'count': [row[3] for row in daily_trends]
        }

        hourly_data = {
            'labels': [f"{row[0]}h" for row in hourly_trends],
            'confidence': [round(row[1] * 100, 1) if row[1] else 0 for row in hourly_trends],
            'processing_time': [round(row[2], 2) if row[2] else 0 for row in hourly_trends],
            'count': [row[3] for row in hourly_trends]
        }

        type_performance = {}
        for row in performance_by_type:
            type_performance[row[0]] = {
                'confidence': round(row[1] * 100, 1) if row[1] else 0,
                'processing_time': round(row[2], 2) if row[2] else 0,
                'count': row[3]
            }

        return jsonify({
            'success': True,
            'data': {
                'daily_trends': daily_data,
                'hourly_trends': hourly_data,
                'performance_by_type': type_performance
            }
        })

    except Exception as e:
        print(f"Erreur performance trends: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

def create_medical_report(data):
    """Créer un rapport médical formaté"""
    analysis_data = data['analysisData']
    current_date = datetime.now().strftime('%d/%m/%Y à %H:%M')

    report = f"""
RAPPORT D'ANALYSE IRM - NEUROSCAN AI
====================================

INFORMATIONS PATIENT
-------------------
Nom: {data.get('patientName', 'Non spécifié')}
Date de naissance: {data.get('patientDob', 'Non spécifiée')}
ID Patient: {data.get('patientId', 'Non spécifié')}
Médecin référent: {data.get('doctorName', 'Non spécifié')}
Date d'analyse: {current_date}

RÉSULTATS DE L'ANALYSE IA
-------------------------
Diagnostic principal: {analysis_data['prediction']}
Niveau de confiance: {analysis_data['confidence'] * 100:.1f}%
Tumeur détectée: {'Oui' if analysis_data['is_tumor'] else 'Non'}

PROBABILITÉS DÉTAILLÉES
-----------------------
- Normal: {analysis_data['probabilities']['Normal'] * 100:.1f}%
- Gliome: {analysis_data['probabilities']['Gliome'] * 100:.1f}%
- Méningiome: {analysis_data['probabilities']['Méningiome'] * 100:.1f}%
- Tumeur pituitaire: {analysis_data['probabilities']['Tumeur pituitaire'] * 100:.1f}%

RECOMMANDATIONS CLINIQUES
-------------------------
"""

    for i, rec in enumerate(analysis_data['recommendations'], 1):
        report += f"{i}. {rec}\n"

    if data.get('clinicalNotes'):
        report += f"""
NOTES CLINIQUES ADDITIONNELLES
------------------------------
{data['clinicalNotes']}
"""

    report += f"""
AVERTISSEMENT MÉDICAL
--------------------
Cette analyse a été générée par un système d'intelligence artificielle
à des fins d'aide au diagnostic. Elle ne remplace pas l'expertise médicale
et doit être interprétée par un professionnel de santé qualifié.

Les résultats doivent être corrélés avec l'examen clinique et d'autres
investigations complémentaires selon les protocoles en vigueur.

Rapport généré par NeuroScan AI - {current_date}
Système certifié CE - Dispositif médical de classe IIa
"""

    return report

def send_analysis_email(data):
    """Simuler l'envoi d'un email de partage"""
    analysis_data = data['analysisData']
    current_date = datetime.now().strftime('%d/%m/%Y à %H:%M')

    # Dans une vraie application, ici on utiliserait un service d'email
    # comme SendGrid, AWS SES, ou SMTP

    email_content = f"""
Objet: Partage d'analyse IRM - NeuroScan AI

Bonjour {data.get('recipientName', 'Collègue')},

{data.get('shareMessage', 'Je partage avec vous cette analyse IRM pour avoir votre avis.')}

RÉSUMÉ DE L'ANALYSE:
- Diagnostic: {analysis_data['prediction']}
- Confiance: {analysis_data['confidence'] * 100:.1f}%
- Date d'analyse: {current_date}

Vous pouvez accéder à l'analyse complète via le lien sécurisé ci-dessous:
[Lien sécurisé vers l'analyse]

Niveau de confidentialité: {data.get('confidentiality', 'Standard')}

Cordialement,
Système NeuroScan AI
"""

    # Log de l'email (en développement)
    print(f"Email simulé envoyé à {data['recipientEmail']}")
    print(f"Contenu: {email_content[:200]}...")

    return True

# ========== NOUVEAUX ENDPOINTS POUR L'ANALYSE COMPARATIVE D'ÉVOLUTION ==========

@app.route('/upload-patient', methods=['POST'])
def upload_patient_exam():
    """Upload et analyse d'IRM avec suivi patient"""
    start_time = time.time()

    try:
        # Vérifier les données du formulaire
        if 'file' not in request.files:
            return jsonify({'error': 'Aucun fichier fourni'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'Aucun fichier sélectionné'}), 400

        # Récupérer les informations du patient
        patient_id = request.form.get('patient_id')
        if not patient_id:
            return jsonify({'error': 'ID patient requis'}), 400

        patient_nom = request.form.get('patient_nom', '')
        patient_prenom = request.form.get('patient_prenom', '')
        patient_date_naissance = request.form.get('patient_date_naissance')
        patient_sexe = request.form.get('patient_sexe')
        medecin_referent = request.form.get('medecin_referent', '')
        exam_date = request.form.get('exam_date')

        # Créer ou récupérer le patient
        create_or_get_patient(
            patient_id, patient_nom, patient_prenom,
            patient_date_naissance, patient_sexe, medecin_referent
        )

        # Traitement de l'image (même logique que l'upload normal)
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)

            # Analyser l'image
            results = predict_tumor(filepath)
            if results is None:
                os.remove(filepath)
                return jsonify({'error': 'Erreur lors de l\'analyse de l\'image'}), 500

            processing_time = time.time() - start_time

            # Encoder l'image en base64
            with open(filepath, 'rb') as img_file:
                img_data = base64.b64encode(img_file.read()).decode('utf-8')
                img_url = f"data:image/jpeg;base64,{img_data}"

            # Nettoyer le fichier temporaire
            os.remove(filepath)

            # Sauvegarder l'examen dans la nouvelle structure
            exam_date_obj = datetime.fromisoformat(exam_date) if exam_date else None
            image_metadata = {
                'filename': filename,
                'upload_time': datetime.now().isoformat(),
                'file_size': len(img_data)
            }

            session_id = request.headers.get('X-Session-ID', 'anonymous')
            ip_address = request.remote_addr

            exam_id = save_exam_to_db(
                patient_id, results, filename, processing_time,
                exam_date_obj, image_metadata, session_id, ip_address
            )

            if exam_id is None:
                return jsonify({'error': 'Erreur lors de la sauvegarde'}), 500

            # Analyser l'évolution si ce n'est pas le premier examen
            evolution_analysis = analyze_tumor_evolution(patient_id, exam_id)

            # Sauvegarder aussi dans l'ancienne table pour compatibilité
            save_analysis_to_db(results, filename, processing_time, session_id, ip_address)

            # Préparer la réponse
            response = {
                'success': True,
                'exam_id': exam_id,
                'patient_id': patient_id,
                'image_url': img_url,
                'prediction': results['predicted_label'],
                'confidence': results['confidence'],
                'probabilities': results['probabilities'],
                'is_tumor': results['predicted_class'] != 0,
                'recommendations': results.get('recommendations', []),
                'description': results.get('description', ''),
                'processing_time': round(processing_time, 2),
                'evolution_analysis': evolution_analysis
            }

            return jsonify(response)

        else:
            return jsonify({'error': 'Type de fichier non autorisé'}), 400

    except Exception as e:
        print(f"Erreur lors de l'upload patient: {e}")
        return jsonify({'error': 'Erreur interne du serveur'}), 500

@app.route('/patient/<patient_id>/history')
def get_patient_history(patient_id):
    """Récupérer l'historique complet d'un patient"""
    try:
        summary = get_patient_evolution_summary(patient_id)

        if summary is None:
            return jsonify({'error': 'Patient non trouvé'}), 404

        # Formater les données pour l'API
        exams_formatted = []
        for exam in summary['exams']:
            exam_data = {
                'id': exam[0],
                'exam_date': exam[2],
                'filename': exam[3],
                'predicted_label': exam[6],
                'confidence': exam[7],
                'probabilities': json.loads(exam[8]) if exam[8] else {},
                'description': exam[9],
                'processing_time': exam[11]
            }
            exams_formatted.append(exam_data)

        evolutions_formatted = []
        for evolution in summary['evolutions']:
            evolution_data = {
                'id': evolution[0],
                'time_interval_days': evolution[4],
                'evolution_type': evolution[5],
                'confidence_change': evolution[6],
                'probability_changes': json.loads(evolution[7]) if evolution[7] else {},
                'alert_level': evolution[9],
                'medical_interpretation': evolution[10],
                'created_at': evolution[12]
            }
            evolutions_formatted.append(evolution_data)

        response = {
            'success': True,
            'patient_id': patient_id,
            'total_exams': summary['total_exams'],
            'evolution_analyses': summary['evolution_analyses'],
            'exams': exams_formatted,
            'evolutions': evolutions_formatted
        }

        return jsonify(response)

    except Exception as e:
        print(f"Erreur lors de la récupération de l'historique: {e}")
        return jsonify({'error': 'Erreur interne du serveur'}), 500

@app.route('/patient/<patient_id>/evolution-report')
def generate_evolution_report(patient_id):
    """Générer un rapport d'évolution complet pour un patient"""
    try:
        summary = get_patient_evolution_summary(patient_id)

        if summary is None:
            return jsonify({'error': 'Patient non trouvé'}), 404

        # Générer le rapport d'évolution
        report = create_evolution_report(summary)

        return jsonify({
            'success': True,
            'patient_id': patient_id,
            'report': report
        })

    except Exception as e:
        print(f"Erreur lors de la génération du rapport d'évolution: {e}")
        return jsonify({'error': 'Erreur interne du serveur'}), 500

@app.route('/patients/alerts')
def get_patient_alerts():
    """Récupérer toutes les alertes médicales actives"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        # Récupérer les alertes critiques et élevées des 30 derniers jours
        cursor.execute('''
            SELECT ea.*, p.patient_id, p.nom, p.prenom
            FROM evolution_analysis ea
            JOIN patients p ON ea.patient_id = p.patient_id
            WHERE ea.alert_level IN ('critical', 'high')
            AND ea.created_at >= datetime('now', '-30 days')
            ORDER BY ea.alert_level DESC, ea.created_at DESC
        ''')

        alerts = cursor.fetchall()
        conn.close()

        alerts_formatted = []
        for alert in alerts:
            alert_data = {
                'id': alert[0],
                'patient_id': alert[13],
                'patient_name': f"{alert[14]} {alert[15]}",
                'evolution_type': alert[5],
                'alert_level': alert[9],
                'medical_interpretation': alert[10],
                'time_interval_days': alert[4],
                'created_at': alert[12]
            }
            alerts_formatted.append(alert_data)

        return jsonify({
            'success': True,
            'total_alerts': len(alerts_formatted),
            'alerts': alerts_formatted
        })

    except Exception as e:
        print(f"Erreur lors de la récupération des alertes: {e}")
        return jsonify({'error': 'Erreur interne du serveur'}), 500

@app.route('/evolution-dashboard')
def evolution_dashboard():
    """Page du tableau de bord d'évolution tumorale"""
    return render_template('evolution_dashboard.html')

@app.route('/patient/<patient_id>/generate-evolution-report')
def generate_patient_evolution_report(patient_id):
    """Générer un rapport d'évolution HTML pour un patient"""
    try:
        from evolution_report_generator import generate_html_evolution_report

        # Générer le rapport HTML
        output_file = generate_html_evolution_report(patient_id)

        if output_file:
            # Lire le contenu du fichier
            with open(output_file, 'r', encoding='utf-8') as f:
                html_content = f.read()

            # Supprimer le fichier temporaire
            os.remove(output_file)

            return Response(html_content, mimetype='text/html')
        else:
            return jsonify({'error': 'Impossible de générer le rapport'}), 500

    except Exception as e:
        print(f"Erreur lors de la génération du rapport: {e}")
        return jsonify({'error': 'Erreur interne du serveur'}), 500

@app.route('/api/evolution/summary')
def get_evolution_summary():
    """API pour obtenir un résumé de tous les patients avec évolution"""
    try:
        conn = sqlite3.connect(DATABASE_PATH)
        cursor = conn.cursor()

        # Récupérer tous les patients avec leurs examens
        cursor.execute('''
            SELECT p.patient_id, p.nom, p.prenom,
                   COUNT(e.id) as total_exams,
                   MIN(e.exam_date) as first_exam,
                   MAX(e.exam_date) as last_exam,
                   COUNT(ea.id) as evolution_analyses
            FROM patients p
            LEFT JOIN examens_irm e ON p.patient_id = e.patient_id
            LEFT JOIN evolution_analysis ea ON p.patient_id = ea.patient_id
            GROUP BY p.patient_id, p.nom, p.prenom
            HAVING total_exams > 0
            ORDER BY last_exam DESC
        ''')

        patients = cursor.fetchall()

        # Récupérer les alertes actives
        cursor.execute('''
            SELECT patient_id, alert_level, COUNT(*) as count
            FROM evolution_analysis
            WHERE alert_level IN ('critical', 'high')
            AND created_at >= datetime('now', '-30 days')
            GROUP BY patient_id, alert_level
        ''')

        alerts = cursor.fetchall()
        conn.close()

        # Organiser les données
        patients_data = []
        for patient in patients:
            patient_alerts = [a for a in alerts if a[0] == patient[0]]
            critical_alerts = sum(a[2] for a in patient_alerts if a[1] == 'critical')
            high_alerts = sum(a[2] for a in patient_alerts if a[1] == 'high')

            patients_data.append({
                'patient_id': patient[0],
                'nom': patient[1],
                'prenom': patient[2],
                'total_exams': patient[3],
                'first_exam': patient[4],
                'last_exam': patient[5],
                'evolution_analyses': patient[6],
                'critical_alerts': critical_alerts,
                'high_alerts': high_alerts,
                'has_alerts': critical_alerts > 0 or high_alerts > 0
            })

        return jsonify({
            'success': True,
            'total_patients': len(patients_data),
            'patients': patients_data
        })

    except Exception as e:
        print(f"Erreur lors de la récupération du résumé: {e}")
        return jsonify({'error': 'Erreur interne du serveur'}), 500

def create_evolution_report(summary):
    """Créer un rapport d'évolution détaillé"""
    patient = summary['patient']
    exams = summary['exams']
    evolutions = summary['evolutions']

    if len(exams) < 2:
        return {
            'type': 'single_exam',
            'message': 'Rapport d\'évolution non disponible - Un seul examen disponible',
            'exam_count': len(exams)
        }

    # Analyser la tendance générale
    first_exam = exams[0]
    last_exam = exams[-1]

    first_probs = json.loads(first_exam[8]) if first_exam[8] else {}
    last_probs = json.loads(last_exam[8]) if last_exam[8] else {}

    # Calculer les changements globaux
    global_changes = {}
    for tumor_type in first_probs.keys():
        change = last_probs.get(tumor_type, 0) - first_probs[tumor_type]
        global_changes[tumor_type] = {
            'initial': first_probs[tumor_type],
            'final': last_probs.get(tumor_type, 0),
            'change': change,
            'change_percent': (change / first_probs[tumor_type] * 100) if first_probs[tumor_type] > 0 else 0
        }

    # Déterminer la tendance générale
    max_change = max(global_changes.values(), key=lambda x: abs(x['change']))

    if abs(max_change['change']) < 0.05:
        overall_trend = 'stable'
    elif max_change['change'] > 0:
        overall_trend = 'progression'
    else:
        overall_trend = 'regression'

    # Compter les alertes par niveau
    alert_counts = {'critical': 0, 'high': 0, 'medium': 0, 'low': 0, 'none': 0}
    for evolution in evolutions:
        alert_level = evolution[9]  # alert_level à l'index 9
        if alert_level in alert_counts:
            alert_counts[alert_level] += 1

    # Calculer la durée totale de suivi
    first_date = datetime.fromisoformat(first_exam[2])
    last_date = datetime.fromisoformat(last_exam[2])
    total_follow_up_days = (last_date - first_date).days

    report = {
        'type': 'evolution_report',
        'patient_info': {
            'patient_id': patient[1],
            'nom': patient[2],
            'prenom': patient[3]
        },
        'summary': {
            'total_exams': len(exams),
            'total_follow_up_days': total_follow_up_days,
            'first_exam_date': first_exam[2],
            'last_exam_date': last_exam[2],
            'overall_trend': overall_trend
        },
        'diagnosis_evolution': {
            'initial_diagnosis': first_exam[6],
            'final_diagnosis': last_exam[6],
            'diagnosis_changed': first_exam[6] != last_exam[6]
        },
        'probability_changes': global_changes,
        'alert_summary': alert_counts,
        'recommendations': generate_evolution_recommendations(overall_trend, alert_counts, total_follow_up_days)
    }

    return report

def generate_evolution_recommendations(overall_trend, alert_counts, follow_up_days):
    """Générer des recommandations basées sur l'évolution"""
    recommendations = []

    # Recommandations basées sur la tendance
    if overall_trend == 'progression':
        recommendations.append("📈 Surveillance renforcée recommandée en raison de la progression observée")
        recommendations.append("🏥 Consultation spécialisée conseillée pour évaluation thérapeutique")
    elif overall_trend == 'regression':
        recommendations.append("📉 Évolution favorable - Maintenir le protocole de suivi actuel")
    else:
        recommendations.append("📊 Évolution stable - Suivi de routine approprié")

    # Recommandations basées sur les alertes
    if alert_counts['critical'] > 0:
        recommendations.append("🚨 URGENT: Alertes critiques détectées - Évaluation immédiate requise")
    elif alert_counts['high'] > 0:
        recommendations.append("⚠️ Alertes importantes - Consultation dans les 48h recommandée")

    # Recommandations basées sur la durée de suivi
    if follow_up_days < 90:
        recommendations.append("⏱️ Suivi récent - Continuer la surveillance rapprochée")
    elif follow_up_days > 365:
        recommendations.append("📅 Suivi à long terme établi - Évaluer la fréquence des contrôles")

    return recommendations

if __name__ == '__main__':
    print(f"Démarrage de l'application sur le device: {device}")
    print(f"Modèle chargé: {'Oui' if model is not None else 'Non'}")
    app.run(debug=True, host='0.0.0.0', port=5000)
