<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tableau de Bord - Évolution Tumorale</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
            display: flex;
            align-items: center;
        }

        .card h3::before {
            font-size: 1.5em;
            margin-right: 10px;
        }

        .stats-card h3::before { content: "📊"; }
        .patients-card h3::before { content: "👥"; }
        .alerts-card h3::before { content: "🚨"; }
        .reports-card h3::before { content: "📋"; }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 1.1em;
        }

        .patients-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .patient-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #ecf0f1;
            transition: background-color 0.3s ease;
        }

        .patient-item:hover {
            background-color: #f8f9fa;
        }

        .patient-info {
            flex-grow: 1;
        }

        .patient-name {
            font-weight: bold;
            color: #2c3e50;
        }

        .patient-details {
            font-size: 0.9em;
            color: #7f8c8d;
            margin-top: 2px;
        }

        .patient-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 15px;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9em;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: scale(1.05);
        }

        .btn-success {
            background: #2ecc71;
            color: white;
        }

        .btn-success:hover {
            background: #27ae60;
            transform: scale(1.05);
        }

        .alert-badge {
            padding: 4px 8px;
            border-radius: 10px;
            font-size: 0.8em;
            font-weight: bold;
            margin-left: 10px;
        }

        .alert-critical {
            background: #e74c3c;
            color: white;
        }

        .alert-high {
            background: #f39c12;
            color: white;
        }

        .alert-none {
            background: #2ecc71;
            color: white;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #7f8c8d;
        }

        .loading::before {
            content: "⏳";
            font-size: 2em;
            display: block;
            margin-bottom: 10px;
        }

        .upload-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .upload-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 5px;
            font-weight: 500;
            color: #2c3e50;
        }

        .form-group input, .form-group select {
            padding: 10px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #3498db;
        }

        .file-input-wrapper {
            position: relative;
            overflow: hidden;
            display: inline-block;
            width: 100%;
        }

        .file-input {
            position: absolute;
            left: -9999px;
        }

        .file-input-label {
            padding: 12px 20px;
            background: #3498db;
            color: white;
            border-radius: 8px;
            cursor: pointer;
            display: block;
            text-align: center;
            transition: background-color 0.3s ease;
        }

        .file-input-label:hover {
            background: #2980b9;
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .upload-form {
                grid-template-columns: 1fr;
            }
            
            .patient-actions {
                flex-direction: column;
                gap: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 Tableau de Bord - Évolution Tumorale</h1>
            <p>Système d'analyse comparative IRM - NeuroScan AI</p>
        </div>

        <!-- Section d'upload pour nouveaux examens -->
        <div class="upload-section">
            <h3>📤 Nouvel Examen IRM avec Suivi Patient</h3>
            <form id="patientUploadForm" enctype="multipart/form-data">
                <div class="upload-form">
                    <div class="form-group">
                        <label for="patient_id">ID Patient *</label>
                        <input type="text" id="patient_id" name="patient_id" required placeholder="PAT001">
                    </div>
                    <div class="form-group">
                        <label for="patient_nom">Nom</label>
                        <input type="text" id="patient_nom" name="patient_nom" placeholder="Dupont">
                    </div>
                    <div class="form-group">
                        <label for="patient_prenom">Prénom</label>
                        <input type="text" id="patient_prenom" name="patient_prenom" placeholder="Marie">
                    </div>
                    <div class="form-group">
                        <label for="patient_sexe">Sexe</label>
                        <select id="patient_sexe" name="patient_sexe">
                            <option value="">Non spécifié</option>
                            <option value="M">Masculin</option>
                            <option value="F">Féminin</option>
                            <option value="Autre">Autre</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="medecin_referent">Médecin Référent</label>
                        <input type="text" id="medecin_referent" name="medecin_referent" placeholder="Dr. Martin">
                    </div>
                    <div class="form-group">
                        <label for="exam_date">Date d'Examen</label>
                        <input type="date" id="exam_date" name="exam_date">
                    </div>
                </div>
                <div class="form-group">
                    <label for="file">Image IRM *</label>
                    <div class="file-input-wrapper">
                        <input type="file" id="file" name="file" class="file-input" accept=".jpg,.jpeg,.png,.dcm,.nii" required>
                        <label for="file" class="file-input-label">📁 Choisir une image IRM</label>
                    </div>
                </div>
                <button type="submit" class="btn btn-primary" style="width: 100%; padding: 15px; font-size: 1.1em;">
                    🔬 Analyser et Comparer avec l'Historique
                </button>
            </form>
        </div>

        <!-- Statistiques générales -->
        <div class="dashboard-grid">
            <div class="card stats-card">
                <h3>Statistiques Générales</h3>
                <div class="stat-number" id="totalPatients">-</div>
                <div class="stat-label">Patients suivis</div>
            </div>

            <div class="card stats-card">
                <h3>Examens Totaux</h3>
                <div class="stat-number" id="totalExams">-</div>
                <div class="stat-label">Examens analysés</div>
            </div>

            <div class="card alerts-card">
                <h3>Alertes Actives</h3>
                <div class="stat-number" id="activeAlerts">-</div>
                <div class="stat-label">Alertes critiques/importantes</div>
            </div>

            <div class="card reports-card">
                <h3>Analyses d'Évolution</h3>
                <div class="stat-number" id="evolutionAnalyses">-</div>
                <div class="stat-label">Comparaisons effectuées</div>
            </div>
        </div>

        <!-- Liste des patients -->
        <div class="card patients-card">
            <h3>Patients avec Suivi d'Évolution</h3>
            <div id="patientsList" class="patients-list">
                <div class="loading">Chargement des données...</div>
            </div>
        </div>
    </div>

    <script>
        // Charger les données au démarrage
        document.addEventListener('DOMContentLoaded', function() {
            loadEvolutionSummary();
            setupFileInput();
            setupUploadForm();
        });

        function setupFileInput() {
            const fileInput = document.getElementById('file');
            const fileLabel = document.querySelector('.file-input-label');
            
            fileInput.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    fileLabel.textContent = `📁 ${this.files[0].name}`;
                } else {
                    fileLabel.textContent = '📁 Choisir une image IRM';
                }
            });
        }

        function setupUploadForm() {
            const form = document.getElementById('patientUploadForm');
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                uploadPatientExam();
            });
        }

        async function uploadPatientExam() {
            const form = document.getElementById('patientUploadForm');
            const formData = new FormData(form);
            
            try {
                const response = await fetch('/upload-patient', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    alert('✅ Examen analysé avec succès!\n\n' + 
                          `Diagnostic: ${result.prediction} (${(result.confidence * 100).toFixed(1)}%)\n` +
                          `Évolution: ${result.evolution_analysis ? 'Analysée' : 'Premier examen'}`);
                    
                    // Recharger les données
                    loadEvolutionSummary();
                    form.reset();
                    document.querySelector('.file-input-label').textContent = '📁 Choisir une image IRM';
                } else {
                    alert('❌ Erreur: ' + result.error);
                }
            } catch (error) {
                alert('❌ Erreur de connexion: ' + error.message);
            }
        }

        async function loadEvolutionSummary() {
            try {
                const response = await fetch('/api/evolution/summary');
                const data = await response.json();
                
                if (data.success) {
                    updateStatistics(data);
                    displayPatients(data.patients);
                } else {
                    console.error('Erreur lors du chargement:', data.error);
                }
            } catch (error) {
                console.error('Erreur de connexion:', error);
            }
        }

        function updateStatistics(data) {
            document.getElementById('totalPatients').textContent = data.total_patients;
            
            const totalExams = data.patients.reduce((sum, p) => sum + p.total_exams, 0);
            document.getElementById('totalExams').textContent = totalExams;
            
            const totalAlerts = data.patients.reduce((sum, p) => sum + p.critical_alerts + p.high_alerts, 0);
            document.getElementById('activeAlerts').textContent = totalAlerts;
            
            const totalEvolutions = data.patients.reduce((sum, p) => sum + p.evolution_analyses, 0);
            document.getElementById('evolutionAnalyses').textContent = totalEvolutions;
        }

        function displayPatients(patients) {
            const container = document.getElementById('patientsList');
            
            if (patients.length === 0) {
                container.innerHTML = '<div class="loading">Aucun patient avec suivi d\'évolution</div>';
                return;
            }
            
            container.innerHTML = patients.map(patient => `
                <div class="patient-item">
                    <div class="patient-info">
                        <div class="patient-name">${patient.nom} ${patient.prenom} (${patient.patient_id})</div>
                        <div class="patient-details">
                            ${patient.total_exams} examens • ${patient.evolution_analyses} analyses d'évolution
                            ${patient.last_exam ? `• Dernier: ${patient.last_exam.split('T')[0]}` : ''}
                        </div>
                    </div>
                    <div class="patient-actions">
                        <a href="/patient/${patient.patient_id}/history" class="btn btn-primary">📊 Historique</a>
                        <a href="/patient/${patient.patient_id}/generate-evolution-report" target="_blank" class="btn btn-success">📋 Rapport</a>
                        ${patient.has_alerts ? `<span class="alert-badge ${patient.critical_alerts > 0 ? 'alert-critical' : 'alert-high'}">
                            ${patient.critical_alerts > 0 ? '🚨 Critique' : '⚠️ Important'}
                        </span>` : '<span class="alert-badge alert-none">✅ Normal</span>'}
                    </div>
                </div>
            `).join('');
        }
    </script>
</body>
</html>
