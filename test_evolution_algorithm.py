#!/usr/bin/env python3
"""
Test complet de l'algorithme d'analyse d'évolution tumorale
"""

import sqlite3
import json
from datetime import datetime, timedelta

DATABASE_PATH = 'neuroscan_analytics.db'

def calculate_evolution_metrics(previous_exam, current_exam):
    """Calculer les métriques d'évolution entre deux examens"""
    try:
        # Parser les probabilités JSON
        prev_probs = json.loads(previous_exam[8])  # probabilities à l'index 8
        curr_probs = json.loads(current_exam[8])
        
        # Calculer l'intervalle de temps
        prev_date = datetime.fromisoformat(previous_exam[2])
        curr_date = datetime.fromisoformat(current_exam[2])
        time_interval = (curr_date - prev_date).days
        
        # Analyser les changements de probabilités
        prob_changes = {}
        max_change = 0
        max_change_type = None
        
        for tumor_type in prev_probs.keys():
            change = curr_probs[tumor_type] - prev_probs[tumor_type]
            prob_changes[tumor_type] = {
                'previous': prev_probs[tumor_type],
                'current': curr_probs[tumor_type],
                'change': change,
                'change_percent': (change / prev_probs[tumor_type] * 100) if prev_probs[tumor_type] > 0 else 0
            }
            
            if abs(change) > abs(max_change):
                max_change = change
                max_change_type = tumor_type
        
        # Déterminer le type d'évolution
        evolution_type = determine_evolution_type(previous_exam, current_exam, prob_changes)
        
        # Calculer le niveau d'alerte
        alert_level = calculate_alert_level(evolution_type, prob_changes, time_interval)
        
        # Générer l'interprétation médicale
        medical_interpretation = generate_medical_interpretation(
            previous_exam, current_exam, prob_changes, evolution_type, time_interval
        )
        
        return {
            'is_first_exam': False,
            'time_interval_days': time_interval,
            'evolution_type': evolution_type,
            'confidence_change': current_exam[7] - previous_exam[7],  # confidence à l'index 7
            'probability_changes': prob_changes,
            'alert_level': alert_level,
            'medical_interpretation': medical_interpretation,
            'previous_diagnosis': previous_exam[6],  # predicted_label à l'index 6
            'current_diagnosis': current_exam[6],
            'max_change_type': max_change_type,
            'max_change_value': max_change
        }
        
    except Exception as e:
        print(f"Erreur lors du calcul des métriques: {e}")
        return None

def determine_evolution_type(previous_exam, current_exam, prob_changes):
    """Déterminer le type d'évolution basé sur les changements"""
    prev_label = previous_exam[6]  # predicted_label
    curr_label = current_exam[6]
    
    # Changement de diagnostic principal
    if prev_label != curr_label:
        return 'changement_type'
    
    # Analyser les changements de probabilités pour le diagnostic actuel
    current_prob_change = prob_changes[curr_label]['change']
    
    if abs(current_prob_change) < 0.05:  # Changement < 5%
        return 'stable'
    elif current_prob_change > 0:
        return 'progression'
    else:
        return 'regression'

def calculate_alert_level(evolution_type, prob_changes, time_interval):
    """Calculer le niveau d'alerte basé sur l'évolution"""
    
    # Changement de type de tumeur
    if evolution_type == 'changement_type':
        # Vérifier si c'est un changement vers une tumeur maligne
        for tumor_type, change_data in prob_changes.items():
            if tumor_type in ['Gliome'] and change_data['change'] > 0.3:
                return 'critical'
        return 'high'
    
    # Progression rapide
    if evolution_type == 'progression':
        max_increase = max([change['change'] for change in prob_changes.values() if change['change'] > 0], default=0)
        
        if time_interval < 90 and max_increase > 0.4:  # >40% en moins de 3 mois
            return 'critical'
        elif time_interval < 180 and max_increase > 0.3:  # >30% en moins de 6 mois
            return 'high'
        elif max_increase > 0.2:  # >20%
            return 'medium'
        else:
            return 'low'
    
    # Régression (amélioration)
    if evolution_type == 'regression':
        return 'low'
    
    # Stable
    return 'none'

def generate_medical_interpretation(previous_exam, current_exam, prob_changes, evolution_type, time_interval):
    """Générer une interprétation médicale de l'évolution"""
    
    prev_label = previous_exam[6]
    curr_label = current_exam[6]
    prev_confidence = previous_exam[7]
    curr_confidence = current_exam[7]
    
    interpretation = []
    
    # Analyse du diagnostic principal
    if prev_label != curr_label:
        interpretation.append(f"Changement de diagnostic : {prev_label} → {curr_label}")
        interpretation.append(f"Confiance : {prev_confidence:.1%} → {curr_confidence:.1%}")
    else:
        interpretation.append(f"Diagnostic stable : {curr_label}")
        confidence_change = curr_confidence - prev_confidence
        if abs(confidence_change) > 0.1:
            direction = "augmentation" if confidence_change > 0 else "diminution"
            interpretation.append(f"Confiance du diagnostic : {direction} de {abs(confidence_change):.1%}")
    
    # Analyse temporelle
    if time_interval < 30:
        interpretation.append(f"Suivi rapproché ({time_interval} jours)")
    elif time_interval < 90:
        interpretation.append(f"Suivi à court terme ({time_interval} jours)")
    elif time_interval < 365:
        interpretation.append(f"Suivi à moyen terme ({time_interval} jours)")
    else:
        interpretation.append(f"Suivi à long terme ({time_interval} jours)")
    
    # Analyse des changements significatifs
    significant_changes = []
    for tumor_type, change_data in prob_changes.items():
        if abs(change_data['change']) > 0.1:  # Changement > 10%
            direction = "↗" if change_data['change'] > 0 else "↘"
            significant_changes.append(f"{tumor_type}: {direction} {abs(change_data['change']):.1%}")
    
    if significant_changes:
        interpretation.append("Changements significatifs : " + ", ".join(significant_changes))
    
    # Recommandations basées sur l'évolution
    if evolution_type == 'changement_type':
        interpretation.append("⚠️ Recommandation : Réévaluation clinique urgente recommandée")
    elif evolution_type == 'progression':
        interpretation.append("📈 Recommandation : Surveillance renforcée et consultation spécialisée")
    elif evolution_type == 'regression':
        interpretation.append("📉 Évolution favorable - Maintenir le suivi habituel")
    else:
        interpretation.append("📊 Évolution stable - Suivi de routine")
    
    return " | ".join(interpretation)

def test_evolution_scenarios():
    """Tester différents scénarios d'évolution"""
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()

    print("🧪 Test des scénarios d'évolution tumorale")
    print("=" * 50)

    # Récupérer les examens de test
    cursor.execute('''
        SELECT * FROM examens_irm 
        WHERE patient_id = 'PAT001' 
        ORDER BY exam_date ASC
    ''')
    exams = cursor.fetchall()

    if len(exams) < 2:
        print("❌ Pas assez d'examens pour tester l'évolution")
        return

    print(f"📊 Analyse de {len(exams)} examens pour le patient PAT001\n")

    # Tester l'évolution entre chaque paire d'examens consécutifs
    for i in range(1, len(exams)):
        previous_exam = exams[i-1]
        current_exam = exams[i]
        
        print(f"🔍 Analyse {i}: Examen {i} → Examen {i+1}")
        print(f"   Dates: {previous_exam[2][:10]} → {current_exam[2][:10]}")
        
        # Calculer les métriques d'évolution
        evolution = calculate_evolution_metrics(previous_exam, current_exam)
        
        if evolution:
            print(f"   Type d'évolution: {evolution['evolution_type']}")
            print(f"   Niveau d'alerte: {evolution['alert_level']}")
            print(f"   Intervalle: {evolution['time_interval_days']} jours")
            print(f"   Changement de confiance: {evolution['confidence_change']:+.2f}")
            print(f"   Diagnostic: {evolution['previous_diagnosis']} → {evolution['current_diagnosis']}")
            
            # Afficher les changements de probabilités significatifs
            print("   Changements de probabilités:")
            for tumor_type, change_data in evolution['probability_changes'].items():
                if abs(change_data['change']) > 0.05:  # Afficher les changements > 5%
                    print(f"     {tumor_type}: {change_data['previous']:.2f} → {change_data['current']:.2f} ({change_data['change']:+.2f})")
            
            print(f"   Interprétation: {evolution['medical_interpretation']}")
            
            # Évaluer la criticité
            if evolution['alert_level'] in ['critical', 'high']:
                print(f"   🚨 ALERTE {evolution['alert_level'].upper()}: Attention médicale requise!")
            elif evolution['alert_level'] == 'medium':
                print(f"   ⚠️ Surveillance recommandée")
            else:
                print(f"   ✅ Évolution normale")
        else:
            print("   ❌ Erreur lors de l'analyse")
        
        print()

    conn.close()

def create_additional_test_scenarios():
    """Créer des scénarios de test supplémentaires"""
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()

    print("🧪 Création de scénarios de test supplémentaires...")

    # Scénario 1: Évolution stable (PAT004)
    cursor.execute('''
        INSERT OR IGNORE INTO patients (patient_id, nom, prenom, sexe, medecin_referent)
        VALUES ('PAT004', 'Stable', 'Patient', 'M', 'Dr. Test')
    ''')

    base_date = datetime.now() - timedelta(days=200)
    stable_probs = {'Normal': 0.05, 'Gliome': 0.05, 'Méningiome': 0.85, 'Tumeur pituitaire': 0.05}
    
    for i in range(3):
        exam_date = base_date + timedelta(days=i*60)
        # Légères variations pour simuler la stabilité
        varied_probs = {k: v + (0.02 * (i-1)) for k, v in stable_probs.items()}
        varied_probs = {k: max(0.01, min(0.98, v)) for k, v in varied_probs.items()}  # Limiter entre 0.01 et 0.98
        
        cursor.execute('''
            INSERT INTO examens_irm 
            (patient_id, exam_date, filename, predicted_class, predicted_label, confidence, probabilities, description)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', ('PAT004', exam_date, f'pat004_exam{i+1}.jpg', 2, 'Méningiome', 0.85, json.dumps(varied_probs), 'Méningiome stable'))

    # Scénario 2: Régression (PAT005)
    cursor.execute('''
        INSERT OR IGNORE INTO patients (patient_id, nom, prenom, sexe, medecin_referent)
        VALUES ('PAT005', 'Regression', 'Patient', 'F', 'Dr. Test')
    ''')

    # Examen 1: Gliome probable
    exam1_date = base_date
    exam1_probs = {'Normal': 0.20, 'Gliome': 0.65, 'Méningiome': 0.10, 'Tumeur pituitaire': 0.05}
    cursor.execute('''
        INSERT INTO examens_irm 
        (patient_id, exam_date, filename, predicted_class, predicted_label, confidence, probabilities, description)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ''', ('PAT005', exam1_date, 'pat005_exam1.jpg', 1, 'Gliome', 0.65, json.dumps(exam1_probs), 'Gliome suspecté'))

    # Examen 2: Amélioration vers normal
    exam2_date = base_date + timedelta(days=90)
    exam2_probs = {'Normal': 0.75, 'Gliome': 0.15, 'Méningiome': 0.08, 'Tumeur pituitaire': 0.02}
    cursor.execute('''
        INSERT INTO examens_irm 
        (patient_id, exam_date, filename, predicted_class, predicted_label, confidence, probabilities, description)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ''', ('PAT005', exam2_date, 'pat005_exam2.jpg', 0, 'Normal', 0.75, json.dumps(exam2_probs), 'Amélioration significative'))

    conn.commit()
    conn.close()
    print("✅ Scénarios supplémentaires créés!")

if __name__ == '__main__':
    print("🧠 Test de l'algorithme d'analyse d'évolution tumorale")
    print("=" * 60)
    
    # Créer des scénarios de test supplémentaires
    create_additional_test_scenarios()
    
    # Tester les scénarios d'évolution
    test_evolution_scenarios()
    
    print("✅ Tests terminés avec succès!")
