#!/usr/bin/env python3
"""
Script de test pour vérifier les nouvelles tables d'évolution tumorale
"""

import sqlite3
import json
from datetime import datetime, timedelta
import random

DATABASE_PATH = 'neuroscan_analytics.db'

def init_evolution_tables():
    """Initialiser les nouvelles tables pour l'évolution tumorale"""
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()

    print("🔄 Création des nouvelles tables...")

    # Table des patients
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS patients (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            patient_id TEXT UNIQUE NOT NULL,
            nom TEXT,
            prenom TEXT,
            date_naissance DATE,
            sexe TEXT CHECK(sexe IN ('M', 'F', 'Autre')),
            medecin_referent TEXT,
            notes_medicales TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Table des examens IRM
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS examens_irm (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            patient_id TEXT NOT NULL,
            exam_date DATETIME NOT NULL,
            filename TEXT,
            image_metadata TEXT,
            predicted_class INTEGER,
            predicted_label TEXT,
            confidence REAL,
            probabilities TEXT,
            description TEXT,
            recommendations TEXT,
            processing_time REAL,
            user_session TEXT,
            ip_address TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (patient_id) REFERENCES patients(patient_id)
        )
    ''')

    # Table d'analyse comparative d'évolution
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS evolution_analysis (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            patient_id TEXT NOT NULL,
            exam_id_previous INTEGER,
            exam_id_current INTEGER,
            time_interval_days INTEGER,
            evolution_type TEXT CHECK(evolution_type IN ('stable', 'progression', 'regression', 'changement_type')),
            confidence_change REAL,
            probability_changes TEXT,
            volume_change_estimate TEXT,
            alert_level TEXT CHECK(alert_level IN ('none', 'low', 'medium', 'high', 'critical')),
            medical_interpretation TEXT,
            recommendations TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (patient_id) REFERENCES patients(patient_id),
            FOREIGN KEY (exam_id_previous) REFERENCES examens_irm(id),
            FOREIGN KEY (exam_id_current) REFERENCES examens_irm(id)
        )
    ''')

    # Index pour optimiser les requêtes
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_patient_exam_date ON examens_irm(patient_id, exam_date)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_evolution_patient ON evolution_analysis(patient_id)')
    cursor.execute('CREATE INDEX IF NOT EXISTS idx_evolution_alert ON evolution_analysis(alert_level)')

    conn.commit()
    conn.close()
    print("✅ Tables créées avec succès!")

def create_test_data():
    """Créer des données de test pour démontrer l'évolution tumorale"""
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()

    print("🧪 Création de données de test...")

    # Créer des patients de test
    test_patients = [
        ('PAT001', 'Dupont', 'Marie', '1975-03-15', 'F', 'Dr. Martin'),
        ('PAT002', 'Bernard', 'Jean', '1968-07-22', 'M', 'Dr. Dubois'),
        ('PAT003', 'Moreau', 'Sophie', '1982-11-08', 'F', 'Dr. Martin')
    ]

    for patient_data in test_patients:
        cursor.execute('''
            INSERT OR IGNORE INTO patients (patient_id, nom, prenom, date_naissance, sexe, medecin_referent)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', patient_data)

    # Créer des examens séquentiels pour PAT001 (évolution progressive)
    base_date = datetime.now() - timedelta(days=365)
    
    # Examen 1 - Normal
    exam1_date = base_date
    exam1_probs = {'Normal': 0.85, 'Gliome': 0.08, 'Méningiome': 0.05, 'Tumeur pituitaire': 0.02}
    cursor.execute('''
        INSERT INTO examens_irm 
        (patient_id, exam_date, filename, predicted_class, predicted_label, confidence, probabilities, description)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ''', ('PAT001', exam1_date, 'pat001_exam1.jpg', 0, 'Normal', 0.85, json.dumps(exam1_probs), 'Structures cérébrales normales'))

    # Examen 2 - Début de suspicion (6 mois plus tard)
    exam2_date = base_date + timedelta(days=180)
    exam2_probs = {'Normal': 0.65, 'Gliome': 0.25, 'Méningiome': 0.08, 'Tumeur pituitaire': 0.02}
    cursor.execute('''
        INSERT INTO examens_irm 
        (patient_id, exam_date, filename, predicted_class, predicted_label, confidence, probabilities, description)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ''', ('PAT001', exam2_date, 'pat001_exam2.jpg', 0, 'Normal', 0.65, json.dumps(exam2_probs), 'Légère anomalie détectée'))

    # Examen 3 - Confirmation de gliome (3 mois plus tard)
    exam3_date = base_date + timedelta(days=270)
    exam3_probs = {'Normal': 0.15, 'Gliome': 0.75, 'Méningiome': 0.08, 'Tumeur pituitaire': 0.02}
    cursor.execute('''
        INSERT INTO examens_irm 
        (patient_id, exam_date, filename, predicted_class, predicted_label, confidence, probabilities, description)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ''', ('PAT001', exam3_date, 'pat001_exam3.jpg', 1, 'Gliome', 0.75, json.dumps(exam3_probs), 'Gliome confirmé'))

    # Créer des examens pour PAT002 (évolution stable)
    for i in range(3):
        exam_date = base_date + timedelta(days=i*120)
        stable_probs = {'Normal': 0.05, 'Gliome': 0.05, 'Méningiome': 0.85, 'Tumeur pituitaire': 0.05}
        cursor.execute('''
            INSERT INTO examens_irm 
            (patient_id, exam_date, filename, predicted_class, predicted_label, confidence, probabilities, description)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', ('PAT002', exam_date, f'pat002_exam{i+1}.jpg', 2, 'Méningiome', 0.85, json.dumps(stable_probs), 'Méningiome stable'))

    conn.commit()
    conn.close()
    print("✅ Données de test créées!")

def test_evolution_analysis():
    """Tester l'analyse d'évolution"""
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()

    print("🔍 Test de l'analyse d'évolution...")

    # Récupérer les examens de PAT001
    cursor.execute('''
        SELECT * FROM examens_irm 
        WHERE patient_id = 'PAT001' 
        ORDER BY exam_date ASC
    ''')
    exams = cursor.fetchall()

    print(f"📊 Patient PAT001 - {len(exams)} examens trouvés:")
    
    for i, exam in enumerate(exams):
        probs = json.loads(exam[8]) if exam[8] else {}
        print(f"  Examen {i+1} ({exam[2][:10]}): {exam[6]} (confiance: {exam[7]:.2f})")
        print(f"    Probabilités: {probs}")

    # Simuler une analyse d'évolution simple
    if len(exams) >= 2:
        print("\n📈 Analyse d'évolution entre le premier et le dernier examen:")
        first_exam = exams[0]
        last_exam = exams[-1]
        
        first_probs = json.loads(first_exam[8])
        last_probs = json.loads(last_exam[8])
        
        print(f"  Diagnostic initial: {first_exam[6]} (confiance: {first_exam[7]:.2f})")
        print(f"  Diagnostic final: {last_exam[6]} (confiance: {last_exam[7]:.2f})")
        
        print("  Changements de probabilités:")
        for tumor_type in first_probs.keys():
            change = last_probs[tumor_type] - first_probs[tumor_type]
            print(f"    {tumor_type}: {first_probs[tumor_type]:.2f} → {last_probs[tumor_type]:.2f} ({change:+.2f})")

    conn.close()

def show_database_structure():
    """Afficher la structure de la base de données"""
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()

    print("🗄️ Structure de la base de données:")
    
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    
    for table in tables:
        table_name = table[0]
        print(f"\n📋 Table: {table_name}")
        
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        
        for col in columns:
            print(f"  - {col[1]} ({col[2]})")

    conn.close()

if __name__ == '__main__':
    print("🧠 Test du système d'évolution tumorale NeuroScan")
    print("=" * 50)
    
    # Initialiser les tables
    init_evolution_tables()
    
    # Créer des données de test
    create_test_data()
    
    # Tester l'analyse
    test_evolution_analysis()
    
    # Afficher la structure
    show_database_structure()
    
    print("\n✅ Test terminé avec succès!")
